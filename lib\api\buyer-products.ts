import { BaseApiClient } from './base-client';
import { API_ENDPOINTS } from './config';
import { handleApiError as handleError } from './errors';

// Create API client instance
const apiClient = new BaseApiClient();

// Types for buyer products API
export interface BuyerProductsQueryParams {
  page?: number;
  limit?: number;
  sort?: string; // Updated to accept any sort value from API
  usePreferences?: boolean;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  gender?: string;
  category?: string;
  categoryId?: string;
  color?: string;
  colors?: string; // Multiple colors comma-separated
  size?: string;
  sizes?: string; // Multiple sizes comma-separated
  brand?: string;
  brands?: string; // Multiple brands comma-separated
  creator?: string;
  countries?: string; // Multiple countries comma-separated
  conditions?: string; // Multiple conditions comma-separated
  type?: string; // Filter by product or bale type
  discountRange?: string; // Filter by discount percentage range
}

export interface ProductItem {
  _id: string;
  type: 'product' | 'bale';
  name: string;
  images: string[];
  basePrice: number;
  status: string;
  maxDiscountPercentage: number;
  ratingsAverage: number;
  ratingsQuantity: number;
  formattedPriceRange: {
    original: {
      min: number;
      max: number;
      isSinglePrice: boolean;
    };
    discounted: {
      min: number;
      max: number;
      isSinglePrice: boolean;
    } | null;
    discountPercentage: number;
  };
  hasAnyDiscount: boolean;
  // Product variation fields
  availableColors?: string[];
  availableSizes?: string[];
  availableIdentifiers?: string[]; // For bales - may not be returned by all APIs
  totalItemsLeft?: number;
  // Bale-specific fields
  country?: string;
  totalItems?: number;
  condition?: string;
}

export interface BuyerProductsResponse {
  status: string;
  results: number;
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  usingPreferences: boolean;
  data: {
    items: ProductItem[];
  };
}

export interface ProductDetailResponse {
  status: string;
  data: {
    product: ProductItem & {
      description?: string;
      brand?: string;
      category?: string;
      gender?: string;
      specifications?: Record<string, any>;
      variations?: Array<{
        _id: string;
        color?: string;
        size?: string;
        identifier?: string; // For bales
        price: number;
        salePrice?: number;
        stock: number;
        images: string[];
      }>;
      highlights?: string[];
      tags?: string[];
      creator?: {
        _id: string;
        name: string;
        shopInfo?: {
          shopName: string;
          logo?: string;
        };
      };
      // Additional fields from API response
      slug?: string;
      seo?: {
        keywords: string[];
      };
      relatedCategories?: string[];
      // Bale-specific fields
      weight?: number;
      dimensions?: {
        length: number;
        width: number;
        height: number;
      };
    };
  };
}

export interface RelatedProductsResponse {
  status: string;
  results: number;
  data: {
    relatedProducts: ProductItem[];
  };
}

export interface ShopProductsResponse {
  status: string;
  results: number;
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  data: {
    products: ProductItem[];
    shop: {
      _id: string;
      name: string;
      shopInfo?: {
        shopName: string;
        logo?: string;
        description?: string;
      };
      ratingsAverage?: number;
      ratingsQuantity?: number;
      totalProducts?: number;
    };
  };
}

// Buyer products API functions
export const buyerProductsApi = {
  // Get products list (public endpoint with optional authentication)
  getProducts: async (params?: BuyerProductsQueryParams): Promise<BuyerProductsResponse> => {
    try {
      // Build query string
      const queryParams = new URLSearchParams();
      
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            queryParams.append(key, value.toString());
          }
        });
      }

      const endpoint = `${API_ENDPOINTS.PRODUCTS.LIST}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      
      // Use authenticated request if user is logged in, otherwise public request
      const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
      
      if (token) {
        return await apiClient.get<BuyerProductsResponse>(endpoint);
      } else {
        return await apiClient.publicGet<BuyerProductsResponse>(endpoint);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      throw handleError(error);
    }
  },

  // Get single product details (public endpoint with optional authentication)
  getProduct: async (productId: string): Promise<ProductDetailResponse> => {
    try {
      const endpoint = `${API_ENDPOINTS.PRODUCTS.DETAIL}/${productId}`;

      // Use authenticated request if user is logged in, otherwise public request
      const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;

      if (token) {
        return await apiClient.get<ProductDetailResponse>(endpoint);
      } else {
        return await apiClient.publicGet<ProductDetailResponse>(endpoint);
      }
    } catch (error) {
      console.error('Error fetching product details:', error);
      throw handleError(error);
    }
  },

  // Get related products (authenticated endpoint)
  getRelatedProducts: async (productId: string): Promise<RelatedProductsResponse> => {
    try {
      const endpoint = `${API_ENDPOINTS.PRODUCTS.RELATED}/${productId}/related`;

      return await apiClient.get<RelatedProductsResponse>(endpoint, {
        requiresAuth: true,
      });
    } catch (error) {
      console.error('Error fetching related products:', error);
      throw handleError(error);
    }
  },

  // Get shop products (authenticated endpoint)
  getShopProducts: async (productId: string, params?: {
    page?: number;
    limit?: number;
  }): Promise<ShopProductsResponse> => {
    try {
      // Build query string
      const queryParams = new URLSearchParams();

      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString());
          }
        });
      }

      const endpoint = `${API_ENDPOINTS.PRODUCTS.SHOP_PRODUCTS}/${productId}/shop-products${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

      return await apiClient.get<ShopProductsResponse>(endpoint, {
        requiresAuth: true,
      });
    } catch (error) {
      console.error('Error fetching shop products:', error);
      throw handleError(error);
    }
  },
};
