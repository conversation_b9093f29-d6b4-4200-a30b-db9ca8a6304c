// Static filter options for the catalog page

export interface SortOption {
  value: string;
  label: string;
}

export interface FilterOptions {
  brands: string[];
  sizes: string[];
  colors: string[];
  genders: string[];
  countries: string[];
  conditions: string[];
  types: string[];
  discountRanges: { value: string; label: string }[];
  sortOptions: SortOption[];
  priceRange: {
    min: number;
    max: number;
  };
}

// Comprehensive list of fashion brands
export const FASHION_BRANDS = [
  // Luxury Brands
  "Gucci", "Louis Vuitton", "Chanel", "Prada", "Hermès", "Dior", "Versace", "Armani", "Dolce & Gabbana", "B<PERSON>berry",
  "Saint Laurent", "Balenciaga", "Givenchy", "Valentino", "Tom Ford", "Bottega Veneta", "Fendi", "Celine", "Loewe",
  
  // High-End Brands
  "Ralph Lauren", "Calvin Klein", "Tommy Hilfiger", "Hugo Boss", "Lacost<PERSON>", "Polo Ralph Lauren", "<PERSON> Ko<PERSON>",
  "<PERSON> Spade", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> Burch", "<PERSON> Baker", "<PERSON> Smith",
  
  // Popular Fashion Brands
  "Nike", "Adidas", "Puma", "Under Armour", "Reebok", "New Balance", "Converse", "Vans", "ASICS", "Fila",
  "Champion", "Jordan", "Supreme", "Off-White", "Stone Island", "Moncler", "Canada Goose", "The North Face",
  
  // Fast Fashion & Accessible Brands
  "Zara", "H&M", "Uniqlo", "Forever 21", "Topshop", "ASOS", "Boohoo", "Shein", "Primark", "Mango",
  "COS", "& Other Stories", "Monki", "Weekday", "Bershka", "Pull & Bear", "Stradivarius",
  
  // Denim Brands
  "Levi's", "Wrangler", "Lee", "Diesel", "G-Star RAW", "True Religion", "7 For All Mankind", "Citizens of Humanity",
  "AG Jeans", "Paige", "Frame", "Acne Studios", "Nudie Jeans", "APC",
  
  // Streetwear & Urban
  "Stussy", "Kith", "Fear of God", "Essentials", "Palm Angels", "Rhude", "Amiri", "Gallery Dept", "Human Made",
  "A Bathing Ape", "Comme des Garçons", "Mastermind Japan", "Neighborhood", "Visvim",
  
  // Women's Fashion
  "Victoria's Secret", "Anthropologie", "Free People", "Urban Outfitters", "Reformation", "Ganni", "Sandro",
  "Maje", "Isabel Marant", "Zimmermann", "Self-Portrait", "Grlfrnd", "Revolve", "Realisation Par",
  
  // Men's Fashion
  "Brunello Cucinelli", "Ermenegildo Zegna", "Canali", "Brioni", "Kiton", "Isaia", "Loro Piana", "Boglioli",
  "Officine Generale", "AMI Paris", "Jacquemus", "Lemaire", "Maison Margiela",
  
  // Children's Brands
  "Carter's", "OshKosh B'gosh", "Gap Kids", "H&M Kids", "Zara Kids", "Nike Kids", "Adidas Kids", "Converse Kids",
  "Polo Ralph Lauren Children", "Tommy Hilfiger Kids", "Guess Kids", "Burberry Children",
  
  // Athletic & Activewear
  "Lululemon", "Athleta", "Alo Yoga", "Outdoor Voices", "Patagonia", "Arc'teryx", "Salomon", "Mammut",
  "Columbia", "REI Co-op", "Smartwool", "Merrell", "Timberland", "Dr. Martens",
  
  // Affordable & Value Brands
  "Target", "Walmart", "Old Navy", "Gap", "Banana Republic", "J.Crew", "American Eagle", "Hollister",
  "Abercrombie & Fitch", "Express", "Ann Taylor", "Loft", "Chico's", "Talbots"
];

// Comprehensive clothing and shoe sizes
export const CLOTHING_SIZES = [
  // Women's Clothing Sizes
  "XXS", "XS", "S", "M", "L", "XL", "XXL", "XXXL", "4XL", "5XL",

  // Numeric Women's Sizes
  "0", "2", "4", "6", "8", "10", "12", "14", "16", "18", "20", "22", "24", "26", "28", "30",

  // Men's Shirt Sizes
  "14.5", "15", "15.5", "17", "17.5", "18.5", "19", "19.5",

  // Waist Sizes (Pants)
  "27", "29", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40",
  "41", "42", "43", "44", "45", "46", "48", "50", "52", "54", "56",

  // Shoe Sizes - Women's US
  "5", "5.5", "6.5", "7", "7.5", "8.5", "9", "9.5", "10.5", "11", "11.5",

  // Shoe Sizes - Men's US
  "12.5", "13", "13.5",

  // Children's Sizes
  "2T", "3T", "4T", "5T", "6X", 

  // Baby Sizes
  "Newborn", "0-3M", "3-6M", "6-9M", "9-12M", "12-18M", "18-24M",

  // International Shoe Sizes (EU)
  "47", "49"
];




// Color options
export const COLORS = [
  // Mapped basic and extended colors
  "Black", "White", "Gray", "Grey", "Red", "Blue", "Green", "Yellow", "Pink", "Purple",
  "Orange", "Brown", "Beige", "Cream", "Khaki", "Maroon", "Teal", "Lime", "Indigo", "Violet",
  "Rose", "Emerald", "Cyan", "Sky", "Amber", "Slate", "Zinc", "Stone", "Neutral", "Navy",
  "Ivory", "Charcoal", "Burgundy", "Coral", "Turquoise", "Lavender", "Mint", "Peach",
  "Salmon", "Gold", "Silver", "Bronze", "Copper", "Olive", "Mustard", "Rust", "Sage",
  "Mauve", "Taupe",
  "Tan", "Rose Gold", "Multicolor", "Floral", "Striped", "Polka Dot", "Plaid", "Animal Print"
];


// Type options
export const TYPES = ["product", "bale"];

// Discount range options
export const DISCOUNT_RANGES = [
  { value: "20", label: "20% and less" },
  { value: "50", label: "50% and less" },
  { value: "70", label: "70% and less" },
  { value: "100", label: "Any discount" }
];

// Gender options
export const GENDERS = ["Male", "Female", "Unisex"];

// Condition options
export const CONDITIONS = ["Excellent", "Good", "Poor"];

// Sort options
export const SORT_OPTIONS: SortOption[] = [
  { value: "newest", label: "Newest First" },
  { value: "price-asc", label: "Price: Low to High" },
  { value: "price-desc", label: "Price: High to Low" },
  { value: "name-asc", label: "Name: A to Z" },
  { value: "name-desc", label: "Name: Z to A" },
  { value: "rating-desc", label: "Highest Rated" },
  { value: "popular", label: "Most Popular" }
];


// Price range
export const PRICE_RANGE = {
  min: 0,
  max: 8000
};

// Countries list (major fashion markets and common shipping destinations)
export const COUNTRIES = [
  "United States", "United Kingdom", "Canada", "Australia", "Germany", "France", "Italy", "Spain",
  "Netherlands", "Belgium", "Switzerland", "Austria", "Sweden", "Norway", "Denmark", "Finland",
  "Japan", "South Korea", "Singapore", "Hong Kong", "China", "India", "Brazil", "Mexico",
  "Argentina", "Chile", "South Africa", "Nigeria", "Kenya", "Ghana", "Egypt", "Morocco",
  "Turkey", "Greece", "Portugal", "Ireland", "Poland", "Czech Republic", "Hungary", "Romania",
  "Bulgaria", "Croatia", "Slovenia", "Slovakia", "Estonia", "Latvia", "Lithuania", "Russia",
  "Ukraine", "Belarus", "Kazakhstan", "Uzbekistan", "Thailand", "Vietnam", "Malaysia", "Indonesia",
  "Philippines", "Taiwan", "New Zealand", "Israel", "UAE", "Saudi Arabia", "Qatar", "Kuwait",
  "Bahrain", "Oman", "Jordan", "Lebanon", "Cyprus", "Malta", "Iceland", "Luxembourg", "Monaco"
];

// Combined filter options
export const FILTER_OPTIONS: FilterOptions = {
  brands: FASHION_BRANDS,
  sizes: CLOTHING_SIZES,
  colors: COLORS,
  genders: GENDERS,
  countries: COUNTRIES,
  conditions: CONDITIONS,
  types: TYPES,
  discountRanges: DISCOUNT_RANGES,
  sortOptions: SORT_OPTIONS,
  priceRange: PRICE_RANGE
};
