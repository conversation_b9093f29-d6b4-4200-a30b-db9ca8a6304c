"use client";

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Sheet, She<PERSON><PERSON><PERSON>ger, She<PERSON><PERSON>ontent, She<PERSON><PERSON>eader, SheetTitle, SheetDescription } from '@/components/ui/sheet';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Filter, X } from 'lucide-react';
import { FILTER_OPTIONS } from '@/lib/constants/filter-options';
import { useCategoriesHierarchy } from '@/lib/hooks/use-categories';
import { MultiCategorySelector } from '@/lib/components/MultiCategorySelector';

const EnhancedFilterSort = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { categories, isLoading: categoriesLoading } = useCategoriesHierarchy();

  // State for all filter options
  const [sortOption, setSortOption] = useState<string>(searchParams.get('sort') || '');
  const [priceRange, setPriceRange] = useState<number[]>([
    Number(searchParams.get('minPrice')) || FILTER_OPTIONS.priceRange.min,
    Number(searchParams.get('maxPrice')) || FILTER_OPTIONS.priceRange.max,
  ]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>(
    searchParams.get('category')?.split(',').filter(Boolean) || []
  );
  const [selectedBrands, setSelectedBrands] = useState<string[]>(
    searchParams.get('brands')?.split(',').filter(Boolean) || []
  );
  const [selectedSizes, setSelectedSizes] = useState<string[]>(
    searchParams.get('sizes')?.split(',').filter(Boolean) || []
  );
  const [selectedColors, setSelectedColors] = useState<string[]>(
    searchParams.get('colors')?.split(',').filter(Boolean) || []
  );
  const [selectedGender, setSelectedGender] = useState<string>(searchParams.get('gender') || '');
  const [selectedCountries, setSelectedCountries] = useState<string[]>(
    searchParams.get('countries')?.split(',').filter(Boolean) || []
  );
  const [selectedConditions, setSelectedConditions] = useState<string[]>(
    searchParams.get('conditions')?.split(',').filter(Boolean) || []
  );
  const [selectedType, setSelectedType] = useState<string>(searchParams.get('type') || '');
  const [selectedDiscountRange, setSelectedDiscountRange] = useState<string>(searchParams.get('discountRange') || '');

  // UI state
  const [isSheetOpen, setIsSheetOpen] = useState<boolean>(false);
  const [showAllBrands, setShowAllBrands] = useState<boolean>(false);
  const [showAllSizes, setShowAllSizes] = useState<boolean>(false);
  const [showAllColors, setShowAllColors] = useState<boolean>(false);
  const [showAllCountries, setShowAllCountries] = useState<boolean>(false);
  const [brandSearch, setBrandSearch] = useState<string>('');
  const [sizeSearch, setSizeSearch] = useState<string>('');
  const [colorSearch, setColorSearch] = useState<string>('');
  const [countrySearch, setCountrySearch] = useState<string>('');

  const updateURLParams = () => {
    const params = new URLSearchParams(searchParams.toString());

    // Sort
    if (sortOption) {
      params.set('sort', sortOption);
    } else {
      params.delete('sort');
    }

    // Price range
    const { min, max } = FILTER_OPTIONS.priceRange;
    if (priceRange[0] > min) params.set('minPrice', priceRange[0].toString());
    else params.delete('minPrice');
    if (priceRange[1] < max) params.set('maxPrice', priceRange[1].toString());
    else params.delete('maxPrice');

    // Categories
    if (selectedCategories.length) {
      params.set('category', selectedCategories.join(','));
    } else {
      params.delete('category');
    }

    // Brands
    if (selectedBrands.length) {
      params.set('brands', selectedBrands.join(','));
    } else {
      params.delete('brands');
    }

    // Sizes
    if (selectedSizes.length) {
      params.set('sizes', selectedSizes.join(','));
    } else {
      params.delete('sizes');
    }

    // Colors
    if (selectedColors.length) {
      params.set('colors', selectedColors.join(','));
    } else {
      params.delete('colors');
    }

    // Gender
    if (selectedGender) {
      params.set('gender', selectedGender);
    } else {
      params.delete('gender');
    }

    // Type
    if (selectedType) {
      params.set('type', selectedType);
    } else {
      params.delete('type');
    }

    // Discount Range
    if (selectedDiscountRange) {
      params.set('discountRange', selectedDiscountRange);
    } else {
      params.delete('discountRange');
    }

    // Countries
    if (selectedCountries.length) {
      params.set('countries', selectedCountries.join(','));
    } else {
      params.delete('countries');
    }

    // Conditions
    if (selectedConditions.length) {
      params.set('conditions', selectedConditions.join(','));
    } else {
      params.delete('conditions');
    }

    router.push(`?${params.toString()}`);
    setIsSheetOpen(false);
  };

  const resetFilters = () => {
    const { min, max } = FILTER_OPTIONS.priceRange;
    setPriceRange([min, max]);
    setSelectedCategories([]);
    setSelectedBrands([]);
    setSelectedSizes([]);
    setSelectedColors([]);
    setSelectedGender('');
    setSelectedType('');
    setSelectedDiscountRange('');
    setSelectedCountries([]);
    setSelectedConditions([]);
    setSortOption('');
    setBrandSearch('');
    setSizeSearch('');
    setColorSearch('');
    setCountrySearch('');
    router.push(window.location.pathname);
    setIsSheetOpen(false);
  };

  // Helper functions for handling selections
  const handleArraySelection = (
    item: string,
    selectedItems: string[],
    setSelectedItems: React.Dispatch<React.SetStateAction<string[]>>
  ) => {
    setSelectedItems((prev) =>
      prev.includes(item) ? prev.filter((i) => i !== item) : [...prev, item]
    );
  };

  // Count active filters
  const activeFiltersCount = [
    selectedCategories.length > 0,
    selectedBrands.length > 0,
    selectedSizes.length > 0,
    selectedColors.length > 0,
    selectedGender !== '',
    selectedType !== '',
    selectedDiscountRange !== '',
    selectedCountries.length > 0,
    selectedConditions.length > 0,
    priceRange[0] > FILTER_OPTIONS.priceRange.min || priceRange[1] < FILTER_OPTIONS.priceRange.max,
    sortOption !== '',
  ].filter(Boolean).length;

  // Filter options with search
  const filteredBrands = brandSearch
    ? FILTER_OPTIONS.brands.filter(brand =>
        brand.toLowerCase().includes(brandSearch.toLowerCase())
      )
    : FILTER_OPTIONS.brands;

  const filteredSizes = sizeSearch
    ? FILTER_OPTIONS.sizes.filter(size =>
        size.toLowerCase().includes(sizeSearch.toLowerCase())
      )
    : FILTER_OPTIONS.sizes;

  const filteredColors = colorSearch
    ? FILTER_OPTIONS.colors.filter(color =>
        color.toLowerCase().includes(colorSearch.toLowerCase())
      )
    : FILTER_OPTIONS.colors;

  const filteredCountries = countrySearch
    ? FILTER_OPTIONS.countries.filter(country =>
        country.toLowerCase().includes(countrySearch.toLowerCase())
      )
    : FILTER_OPTIONS.countries;
  return (
    <div className="flex justify-end">
      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetTrigger asChild>
          <Button variant="outline" className="relative px-2 mr-2">
            <Filter className="w-4 h-4 " />
            <span className='-ml-1'>Filter & Sort</span>
            {activeFiltersCount > 0 && (
              <Badge variant="destructive" className="ml-1 px-1 py-0 text-xs h-5 w-5 rounded-full flex items-center justify-center">
                {activeFiltersCount}
              </Badge>
            )}
          </Button>
        </SheetTrigger>
        <SheetContent className="w-full sm:max-w-md">
          <SheetHeader>
            <SheetTitle>Filter & Sort</SheetTitle>
            <SheetDescription>
              Filter products by categories, brands, sizes, and more. Sort by price, popularity, or newest items.
            </SheetDescription>
          </SheetHeader>

          <div className="h-[calc(100vh-265px)] overflow-y-auto pr-4">
            <div className="space-y-6 mt-4">
              {/* Sort Options */}
              <div>
                <h3 className="font-semibold mb-3">Sort By</h3>
                <RadioGroup value={sortOption} onValueChange={setSortOption}>
                  {FILTER_OPTIONS.sortOptions.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <RadioGroupItem value={option.value} id={option.value} />
                      <label htmlFor={option.value} className="text-sm cursor-pointer">{option.label}</label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <Separator />

              {/* Price Range */}
              <div>
                <h3 className="font-semibold mb-3">Price Range</h3>
                <Slider
                  min={FILTER_OPTIONS.priceRange.min}
                  max={FILTER_OPTIONS.priceRange.max}
                  value={priceRange}
                  onValueChange={setPriceRange}
                  className="mb-2"
                />
                <p className="text-sm text-gray-600">
                  GH₵ {priceRange[0]} - GH₵ {priceRange[1]}
                </p>
              </div>

              <Separator />

              {/* Type Filter */}
              <div>
                <h3 className="font-semibold mb-3">Item Type</h3>
                <RadioGroup value={selectedType} onValueChange={setSelectedType}>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="" id="all-types" />
                    <label htmlFor="all-types" className="text-sm cursor-pointer">All</label>
                  </div>
                  {FILTER_OPTIONS.types.map((type) => (
                    <div key={type} className="flex items-center space-x-2">
                      <RadioGroupItem value={type} id={type} />
                      <label htmlFor={type} className="text-sm cursor-pointer capitalize">{type}</label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <Separator />

              {/* Discount Range Filter */}
              <div>
                <h3 className="font-semibold mb-3">Discount</h3>
                <RadioGroup value={selectedDiscountRange} onValueChange={setSelectedDiscountRange}>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="" id="all-discounts" />
                    <label htmlFor="all-discounts" className="text-sm cursor-pointer">All</label>
                  </div>
                  {FILTER_OPTIONS.discountRanges.map((discount) => (
                    <div key={discount.value} className="flex items-center space-x-2">
                      <RadioGroupItem value={discount.value} id={discount.value} />
                      <label htmlFor={discount.value} className="text-sm cursor-pointer">{discount.label}</label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <Separator />

              {/* Gender Filter */}
              <div>
                <h3 className="font-semibold mb-3">Gender</h3>
                <RadioGroup value={selectedGender} onValueChange={setSelectedGender}>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="" id="all-genders" />
                    <label htmlFor="all-genders" className="text-sm cursor-pointer">All</label>
                  </div>
                  {FILTER_OPTIONS.genders.map((gender) => (
                    <div key={gender} className="flex items-center space-x-2">
                      <RadioGroupItem value={gender} id={gender} />
                      <label htmlFor={gender} className="text-sm cursor-pointer">{gender}</label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <Separator />

              {/* Category Filter */}
              {!categoriesLoading && categories && (
                <div>
                  <h3 className="font-semibold mb-3">Categories</h3>
                  <MultiCategorySelector
                    selectedCategories={selectedCategories}
                    onCategoriesChange={setSelectedCategories}
                    placeholder="Select categories..."
                    maxSelections={5}
                  />
                </div>
              )}

              <Separator />
              {/* Brand Filter */}
              <div>
                <h3 className="font-semibold mb-3">Brands</h3>
                <Input
                  placeholder="Search brands..."
                  value={brandSearch}
                  onChange={(e) => setBrandSearch(e.target.value)}
                  className="mb-3"
                />
                <div className="max-h-40 overflow-y-auto space-y-2">
                  {(showAllBrands ? filteredBrands : filteredBrands.slice(0, 8)).map((brand) => (
                    <div key={brand} className="flex items-center space-x-2">
                      <Checkbox
                        id={brand}
                        checked={selectedBrands.includes(brand)}
                        onCheckedChange={() => handleArraySelection(brand, selectedBrands, setSelectedBrands)}
                      />
                      <label htmlFor={brand} className="text-sm cursor-pointer">{brand}</label>
                    </div>
                  ))}
                </div>
                {!showAllBrands && filteredBrands.length > 8 && (
                  <Button variant="link" onClick={() => setShowAllBrands(true)} className="p-0 h-auto">
                    View More ({filteredBrands.length - 8} more)
                  </Button>
                )}
              </div>

              <Separator />

              {/* Size Filter */}
              <div>
                <h3 className="font-semibold mb-3">Sizes</h3>
                <Input
                  placeholder="Search sizes..."
                  value={sizeSearch}
                  onChange={(e) => setSizeSearch(e.target.value)}
                  className="mb-3"
                />
                <div className="max-h-40 overflow-y-auto">
                  <div className="flex flex-wrap gap-2">
                    {(showAllSizes ? filteredSizes : filteredSizes.slice(0, 20)).map((size) => (
                      <Badge
                        key={size}
                        variant={selectedSizes.includes(size) ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => handleArraySelection(size, selectedSizes, setSelectedSizes)}
                      >
                        {size}
                      </Badge>
                    ))}
                  </div>
                  {!showAllSizes && filteredSizes.length > 20 && (
                    <Button variant="link" onClick={() => setShowAllSizes(true)} className="p-0 h-auto mt-2">
                      View More ({filteredSizes.length - 20} more)
                    </Button>
                  )}
                </div>
              </div>

              <Separator />

              {/* Color Filter */}
              <div>
                <h3 className="font-semibold mb-3">Colors</h3>
                <Input
                  placeholder="Search colors..."
                  value={colorSearch}
                  onChange={(e) => setColorSearch(e.target.value)}
                  className="mb-3"
                />
                <div className="max-h-40 overflow-y-auto">
                  <div className="flex flex-wrap gap-2">
                    {(showAllColors ? filteredColors : filteredColors.slice(0, 20)).map((color) => (
                      <Badge
                        key={color}
                        variant={selectedColors.includes(color) ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => handleArraySelection(color, selectedColors, setSelectedColors)}
                      >
                        {color}
                      </Badge>
                    ))}
                  </div>
                  {!showAllColors && filteredColors.length > 20 && (
                    <Button variant="link" onClick={() => setShowAllColors(true)} className="p-0 h-auto mt-2">
                      View More ({filteredColors.length - 20} more)
                    </Button>
                  )}
                </div>
              </div>

              <Separator />

              {/* Countries Filter */}
              <div>
                <h3 className="font-semibold mb-3">Countries</h3>
                <Input
                  placeholder="Search countries..."
                  value={countrySearch}
                  onChange={(e) => setCountrySearch(e.target.value)}
                  className="mb-3"
                />
                <div className="max-h-40 overflow-y-auto space-y-2">
                  {(showAllCountries ? filteredCountries : filteredCountries.slice(0, 8)).map((country) => (
                    <div key={country} className="flex items-center space-x-2">
                      <Checkbox
                        id={country}
                        checked={selectedCountries.includes(country)}
                        onCheckedChange={() => handleArraySelection(country, selectedCountries, setSelectedCountries)}
                      />
                      <label htmlFor={country} className="text-sm cursor-pointer">{country}</label>
                    </div>
                  ))}
                </div>
                {!showAllCountries && filteredCountries.length > 8 && (
                  <Button variant="link" onClick={() => setShowAllCountries(true)} className="p-0 h-auto">
                    View More ({filteredCountries.length - 8} more)
                  </Button>
                )}
              </div>

              <Separator />

              {/* Conditions Filter */}
              <div>
                <h3 className="font-semibold mb-3">Conditions</h3>
                <div className="space-y-2">
                  {FILTER_OPTIONS.conditions.map((condition) => (
                    <div key={condition} className="flex items-center space-x-2">
                      <Checkbox
                        id={condition}
                        checked={selectedConditions.includes(condition)}
                        onCheckedChange={() => handleArraySelection(condition, selectedConditions, setSelectedConditions)}
                      />
                      <label htmlFor={condition} className="text-sm cursor-pointer">{condition}</label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-between mt-6 pt-4 border-t">
            <Button variant="outline" onClick={resetFilters}>
              Reset All
            </Button>
            <Button onClick={updateURLParams}>
              Apply Filters
            </Button>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default EnhancedFilterSort;
