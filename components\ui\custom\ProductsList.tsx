"use client";

import React, { Suspense, useMemo, useEffect } from 'react';
import ProductCard from './ProductCard';
import { useSearchParams, useRouter } from 'next/navigation';
import { useBuyerProducts } from '@/lib/hooks/use-buyer-products';
import { BuyerProductsQueryParams, ProductItem } from '@/lib/api/buyer-products';
import LottieLoader from '@/components/ui/LottieLoader';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import PaginationComponent from './PaginationComponent';

// Helper function to convert API product to ProductCard format
const convertApiProductToCardFormat = (apiProduct: ProductItem) => {
  const hasDiscount = apiProduct.hasAnyDiscount && apiProduct.maxDiscountPercentage > 0;
  const displayPrice = hasDiscount
    ? apiProduct.formattedPriceRange.discounted?.min || apiProduct.formattedPriceRange.original.min
    : apiProduct.formattedPriceRange.original.min;

  const isBale = apiProduct.type === 'bale';

  return {
    id: apiProduct._id,
    image: apiProduct.images[0] || '/images/placeholder.jpg',
    name: apiProduct.name,
    price: displayPrice,
    discount: hasDiscount ? apiProduct.maxDiscountPercentage : undefined,
    itemsLeft: apiProduct.totalItemsLeft || 0,
    type: apiProduct.type,
    // For products: use availableColors and availableSizes
    availableColors: !isBale ? (apiProduct.availableColors || []) : undefined,
    availableSizes: !isBale ? (apiProduct.availableSizes || []) : undefined,
    // For bales: use availableIdentifiers if available, otherwise fall back to availableSizes as identifiers
    availableIdentifiers: isBale ? (
      apiProduct.availableIdentifiers ||
      (apiProduct.availableSizes?.length ? apiProduct.availableSizes : [])
    ) : undefined,
  };
};

const ProductListContent: React.FC = () => {
  const searchParams = useSearchParams();
  const router = useRouter();

  // Extract query parameters from URL
  const queryParams: BuyerProductsQueryParams = useMemo(() => {
    const params: BuyerProductsQueryParams = {
      page: parseInt(searchParams.get('page') || '1', 10),
      limit: 20, // Fixed limit for now
    };

    // Add search query
    const searchQuery = searchParams.get('q');
    if (searchQuery) params.search = searchQuery;

    // Add category filter
    const category = searchParams.get('category');
    if (category) params.category = category;

    // Add brand filter
    const brands = searchParams.get('brands');
    if (brands) params.brands = brands;

    // Add sort parameter (use API sort values directly)
    const sort = searchParams.get('sort');
    if (sort) params.sort = sort;

    // Add price filters
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    if (minPrice) params.minPrice = parseInt(minPrice, 10);
    if (maxPrice) params.maxPrice = parseInt(maxPrice, 10);

    // Add size filter
    const sizes = searchParams.get('sizes');
    if (sizes) params.sizes = sizes;

    // Add colors filter
    const colors = searchParams.get('colors');
    if (colors) params.colors = colors;

    // Add gender filter
    const gender = searchParams.get('gender');
    if (gender) params.gender = gender;

    // Add type filter
    const type = searchParams.get('type');
    if (type) params.type = type;

    // Add discount range filter
    const discountRange = searchParams.get('discountRange');
    if (discountRange) params.discountRange = discountRange;

    // Add countries filter
    const countries = searchParams.get('countries');
    if (countries) params.countries = countries;

    // Add conditions filter
    const conditions = searchParams.get('conditions');
    if (conditions) params.conditions = conditions;

    return params;
  }, [searchParams]);

  // Fetch products using the API
  const { data: productsResponse, isLoading, error, isError } = useBuyerProducts(queryParams);

  // Show error toast when there's an error
  useEffect(() => {
    if (isError && error) {
      toast({
        variant: "destructive",
        title: "Failed to load products",
        description: "There was an issue loading the products. Please try again.",
      });
    }
  }, [isError, error]);

  // Function to update the page parameter in the URL
  const updatePage = (newPage: number) => {
    const params = new URLSearchParams(searchParams);
    params.set('page', newPage.toString());
    router.push(`?${params.toString()}`);
  };

  // Loading state
  if (isLoading) {
    return (
      <section className="w-full flex items-center justify-center py-12">
        <LottieLoader
          size="lg"
          text="Loading products..."
          textSize="md"
          centered={true}
        />
      </section>
    );
  }

  // Retry function
  const handleRetry = () => {
    // Force a page reload to retry the API call
    window.location.reload();
  };

  // Error state
  if (isError || !productsResponse) {
    return (
      <section className="w-full flex flex-col items-center justify-center py-12 text-center">
        <div className="text-gray-600 mb-4">
          <p className="text-lg font-semibold">Oops! Something went wrong</p>
          <p className="text-sm">We couldn't load the products. Please check your connection and try again.</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleRetry}
            variant="outline"
            className="mt-2"
          >
            Try Again
          </Button>
          <Button
            onClick={() => router.push('/')}
            variant="ghost"
            className="mt-2"
          >
            Go Home
          </Button>
        </div>
      </section>
    );
  }

  const { data: { items: products }, page, totalPages, results, total } = productsResponse;

  // Empty state
  if (!products || products.length === 0) {
    return (
      <section className="w-full flex flex-col items-center justify-center py-12 text-center">
        <div className="text-gray-600">
          <p className="text-lg font-semibold">No products found</p>
          <p className="text-sm">Try adjusting your search or filters to find what you're looking for.</p>
        </div>
      </section>
    );
  }

  return (
    <section className="w-full">
      {/* Results info */}
      <div className="mb-4 text-sm text-gray-600">
        Showing {results} of {total} products
      </div>

      {/* Products grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
        {products.map((product) => (
          <ProductCard
            key={product._id}
            product={convertApiProductToCardFormat(product)}
            cardType="withAddToCart"
          />
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <PaginationComponent
          totalPages={totalPages}
          currentPage={page}
          onPageChange={updatePage}
        />
      )}
    </section>
  );
};

const ProductList: React.FC = () => {
  return (
    <Suspense fallback={
      <section className="w-full flex items-center justify-center py-12">
        <LottieLoader
          size="lg"
          text="Loading products..."
          textSize="md"
          centered={true}
        />
      </section>
    }>
      <ProductListContent />
    </Suspense>
  );
};

export default ProductList;
