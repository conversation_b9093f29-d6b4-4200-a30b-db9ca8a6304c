"use client";

import React from 'react';
import ProductsCarousel from './ProductsCarousel';
import ProductCard from '@/components/ui/custom/ProductCard';
import ProductCarouselSkeleton from './ProductCarouselSkeleton';
import { useBuyerRelatedProducts } from '@/lib/hooks/use-buyer-products';
import { useAuth } from '@/lib/stores/auth-store';

interface RelatedProductsSectionProps {
  productId: string;
  title?: string;
}

const RelatedProductsSection: React.FC<RelatedProductsSectionProps> = ({ 
  productId, 
  title = "Customers also viewed" 
}) => {
  const { isAuthenticated } = useAuth();
  
  // Only fetch if user is authenticated
  const { data: relatedResponse, isLoading, error } = useBuyerRelatedProducts(
    productId,
    { enabled: !isAuthenticated }
  );

  // Don't render anything if user is not authenticated
  if (!isAuthenticated) {
    return null;
  }

  // Show skeleton while loading
  if (isLoading) {
    return <ProductCarouselSkeleton title={title} itemCount={4} />;
  }

  // Don't show anything if there's an error or no products
  if (error || !relatedResponse?.data?.relatedProducts?.length) {
    return null;
  }

  const relatedProducts = relatedResponse.data.relatedProducts;

  return (
    <section className='bg-white my-2 py-3 px-3 rounded shadow-sm'>
      <h1 className="text-sm font-semibold text-gray-600 pb-2">{title}</h1>
      <ProductsCarousel>
        {relatedProducts.map((product) => (
          <div key={product._id} className="p-1">
            <ProductCard
              product={{
                id: product._id || '',
                name: product.name || '',
                image: (product.images && product.images[0]) || '/placeholder-image.jpg',
                price: product.formattedPriceRange?.discounted?.min || product.formattedPriceRange?.original?.min || 0,
                discount: product.maxDiscountPercentage || 0,
                itemsLeft: product.totalItemsLeft || 0,
                availableColors: product.availableColors || [],
                availableSizes: product.availableSizes || [],
                availableIdentifiers: product.availableIdentifiers || [],
                type: product.type || 'product',
              }}
              cardType='normal'
            />
          </div>
        ))}
      </ProductsCarousel>
    </section>
  );
};

export default RelatedProductsSection;
