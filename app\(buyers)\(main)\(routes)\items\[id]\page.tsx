'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import ProductDetailsCarousel from '@/app/(buyers)/(main)/_components/ProductDetailsCarousel';
import ProductDetailsComponent from '@/app/(buyers)/(main)/_components/ProductDetailsComponent';
import Image from 'next/image';
import { useBuyerProduct } from '@/lib/hooks/use-buyer-products';
import LottieLoader from '@/components/ui/LottieLoader';
import { toast } from '@/hooks/use-toast';

const Product = () => {
  const params = useParams();
  const productId = params.id as string;

  // Fetch product details using the API
  const { data: productResponse, isLoading, error } = useBuyerProduct(productId);

  // Show loading state
  if (isLoading) {
    return (
      <section className="w-full mt-20 flex items-center justify-center py-12">
        <LottieLoader
          size="lg"
          text="Loading product details..."
          textSize="md"
          centered={true}
        />
      </section>
    );
  }

  // Show error state
  if (error || !productResponse?.data?.product) {
    // Show error toast
    React.useEffect(() => {
      if (error) {
        toast({
          variant: "destructive",
          title: "Failed to load product",
          description: "There was an issue loading the product details. Please try again.",
        });
      }
    }, [error]);

    return (
      <section className="w-full mt-20 flex items-center justify-center py-12">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Product not found</h2>
          <p className="text-gray-600">The product you're looking for doesn't exist or has been removed.</p>
        </div>
      </section>
    );
  }

  const product = productResponse.data.product;

  // Transform API data to match component expectations
  const transformedProduct = {
    id: parseInt(product._id) || 0,
    name: product.name || '',
    brand: product.brand || '',
    category: product.category || '',
    price: product.formattedPriceRange?.discounted?.min || product.formattedPriceRange?.original?.min || 0,
    discount: product.maxDiscountPercentage || 0,
    images: product.images || [],
    description: product.description || '',
    specifications: product.specifications || {},
    availableSizes: product.availableSizes || [],
    availableColors: product.availableColors || [],
    ratings: product.ratingsAverage || 0,
    reviews: [], // Will be handled separately in future
    stock: product.totalItemsLeft || 0,
    // Additional fields for bales
    ...(product.type === 'bale' && {
      country: product.country,
      totalItems: product.totalItems,
      condition: product.condition,
      availableIdentifiers: product.availableIdentifiers,
    })
  };

  // Calculate unavailable sizes (placeholder logic - can be enhanced based on stock data)
  const unavailableSizes: string[] = [];

  return (
    <section className="w-full mt-20">
      {/* Product Images Carousel */}
      <ProductDetailsCarousel>
        {transformedProduct.images.map((image, index) => (
          <section
            key={index}
            className="h-[50vh] w-full flex justify-center items-center bg-white rounded-xl shadow-md overflow-hidden"
          >
            <Image
              src={image}
              alt={`${transformedProduct.name} - Image ${index + 1}`}
              width={500}
              height={500}
              className="object-contain h-full w-auto"
            />
          </section>
        ))}
      </ProductDetailsCarousel>

      {/* Product Details Component */}
      <ProductDetailsComponent
        product={transformedProduct}
        unavailableSizes={unavailableSizes}
        productId={productId}
      />
    </section>
  );
};

export default Product;