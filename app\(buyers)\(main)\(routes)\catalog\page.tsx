"use client";

import { useSearchParams } from "next/navigation";
import EnhancedFilterSort from "../../_components/EnhancedFilterSort";
import ProductsGrid from "@/components/ui/custom/ProductsGrid";
import ShopsList from "@/components/ui/custom/ShopsList";
import { useCategoriesHierarchy } from "@/lib/hooks/use-categories";
import { useSearch } from "@/lib/hooks/use-search";
import { useBuyerProducts } from "@/lib/hooks/use-buyer-products";
import PaginationComponent from "@/components/ui/custom/PaginationComponent";

const CatalogPageContent = () => {
  const searchParams = useSearchParams();
  const { categories } = useCategoriesHierarchy();

  const searchQuery = searchParams.get("q");
  const category = searchParams.get("category");
  const brands = searchParams.get("brands");
  const sort = searchParams.get("sort");
  const minPrice = searchParams.get("minPrice");
  const maxPrice = searchParams.get("maxPrice");
  const sizes = searchParams.get("sizes");
  const colors = searchParams.get("colors");
  const gender = searchParams.get("gender");
  const type = searchParams.get("type");
  const discountRange = searchParams.get("discountRange");
  const countries = searchParams.get("countries");
  const conditions = searchParams.get("conditions");

  // Extract pagination parameters
  const currentPage = parseInt(searchParams.get("page") || "1", 10);
  const itemsPerPage = 20; // Fixed items per page

  // Prepare filters for products API
  const productFilters = {
    category: category || undefined,
    brands: brands || undefined,
    sizes: sizes || undefined,
    colors: colors || undefined,
    gender: gender || undefined,
    type: type || undefined,
    discountRange: discountRange || undefined,
    countries: countries || undefined,
    conditions: conditions || undefined,
    minPrice: minPrice ? Number(minPrice) : undefined,
    maxPrice: maxPrice ? Number(maxPrice) : undefined,
    sort: sort || undefined,
    page: currentPage,
    limit: itemsPerPage,
  };

  // Prepare search filters (only when there's a search query)
  const searchFilters = {
    q: searchQuery || undefined,
    category: category || undefined,
    brands: brands || undefined,
    sizes: sizes || undefined,
    colors: colors || undefined,
    gender: gender || undefined,
    type: type || undefined,
    discountRange: discountRange || undefined,
    countries: countries || undefined,
    conditions: conditions || undefined,
    minPrice: minPrice || undefined,
    maxPrice: maxPrice || undefined,
    sort: sort || undefined,
    page: currentPage,
    limit: itemsPerPage,
  };

  // Use search API only when there's a search query, otherwise use products API
  const { data: searchResults, isLoading: searchLoading, error: searchError } = useSearch(
    searchFilters,
    { enabled: !!searchQuery }
  );

  const { data: productsData, isLoading: productsLoading, error: productsError } = useBuyerProducts(
    productFilters,
    { enabled: !searchQuery }
  );

  // Determine which data to use
  const isLoading = searchQuery ? searchLoading : productsLoading;
  const error = searchQuery ? searchError : productsError;

  // Helper function to get category names from IDs (handles single or comma-separated)
  const getCategoryNames = (categoryParam: string): string => {
    if (!categories || !categoryParam) return categoryParam;

    const findCategoryInTree = (cats: any[], id: string): any => {
      for (const cat of cats) {
        if (cat._id === id) return cat;
        if (cat.immediateChildren) {
          const found = findCategoryInTree(cat.immediateChildren, id);
          if (found) return found;
        }
      }
      return null;
    };

    // Handle comma-separated category IDs
    const categoryIds = categoryParam.split(',').map(id => id.trim());
    const categoryNames = categoryIds.map(id => {
      const foundCategory = findCategoryInTree(categories, id);
      return foundCategory ? (foundCategory.description || foundCategory.name) : id;
    });

    // Return formatted string
    if (categoryNames.length === 1) {
      return categoryNames[0];
    } else if (categoryNames.length <= 3) {
      return categoryNames.join(', ');
    } else {
      return `${categoryNames.slice(0, 2).join(', ')} +${categoryNames.length - 2} more`;
    }
  };

  // The header adapts based on filters
  let headerText = "All Products";
  let subHeaderText = "";

  // Check if **any filter is active**
  const hasFilters =
    searchQuery ||
    category ||
    brands ||
    sort ||
    minPrice ||
    maxPrice ||
    sizes ||
    colors ||
    gender ||
    type ||
    discountRange ||
    countries ||
    conditions;

  if (hasFilters) {
    if (searchQuery) {
      headerText = `Search Results for "${searchQuery}"`;
    } else {
      headerText = "Filtered Products";
      // Build a descriptive sub-header
      const activeFilters = [];
      if (category) activeFilters.push(`Category: ${getCategoryNames(category)}`);
      if (brands) activeFilters.push(`Brand: ${brands}`);
      if (gender) activeFilters.push(`Gender: ${gender}`);
      if (type) activeFilters.push(`Type: ${type === 'product' ? 'Product' : 'Bale'}`);
      if (minPrice || maxPrice) {
        const priceFilter = minPrice && maxPrice
          ? `Price: GH₵${minPrice} - GH₵${maxPrice}`
          : minPrice
          ? `Price: From GH₵${minPrice}`
          : `Price: Up to GH₵${maxPrice}`;
        activeFilters.push(priceFilter);
      }
      if (sizes) activeFilters.push(`Size: ${sizes}`);
      if (colors) activeFilters.push(`Colors: ${colors}`);
      if (discountRange) {
        const discountText = discountRange === '20' ? '20% and less'
          : discountRange === '50' ? '50% and less'
          : discountRange === '70' ? '70% and less'
          : 'Any discount';
        activeFilters.push(`Discount: ${discountText}`);
      }
      if (countries) activeFilters.push(`Countries: ${countries}`);
      if (conditions) activeFilters.push(`Conditions: ${conditions}`);
      if (sort) activeFilters.push(`Sorted by: ${sort}`);

      subHeaderText = activeFilters.slice(0, 3).join(" • ");
      if (activeFilters.length > 3) {
        subHeaderText += ` • +${activeFilters.length - 3} more`;
      }
    }
  }

  // Show loading state
  if (isLoading) {
    return (
      <section className="w-full mt-20 flex items-center justify-center py-12">
        <LottieLoader
          size="md"
          text="Loading catalog..."
          textSize="md"
          centered={true}
        />
      </section>
    );
  }

  // Show error state
  if (error) {
    return (
      <section className="w-full mt-20">
        <div className="text-center py-12">
          <p className="text-red-600 mb-2">Error loading catalog</p>
          <p className="text-sm text-gray-500">{error.message}</p>
        </div>
      </section>
    );
  }

  // Extract data based on which API is being used
  let products: any[] = [];
  let bales: any[] = [];
  let shops: any[] = [];
  let totalItems = 0;
  let totalPages = 0;

  if (searchQuery) {
    // Using search API - has products, bales, and shops
    products = searchResults?.data?.products || [];
    bales = searchResults?.data?.bales || [];
    shops = searchResults?.data?.shops || [];
    // For search API, total is at the root level
    totalItems = searchResults?.total || 0;
    totalPages = Math.ceil(totalItems / itemsPerPage);
  } else {
    // Using products API - only has items (which includes both products and bales)
    const allItems = productsData?.data?.items || [];
    products = allItems.filter((item: any) => item.type === 'product');
    bales = allItems.filter((item: any) => item.type === 'bale');
    shops = []; // Products API doesn't return shops
    totalItems = productsData?.total || 0;
    totalPages = productsData?.totalPages || Math.ceil(totalItems / itemsPerPage);
  }

  // Combine products and bales for the ProductList component
  const allProducts = [...products, ...bales];

  return (
    <section className="w-full mt-20">
      {/* Header and Filter/Sort Layout */}
      <div className="flex justify-between items-start mb-6">
        {/* Left side - Header Text */}
        <div className="flex-1">
          <h1 className="text-gray-700 font-semibold text-lg mb-1">
            {headerText}
          </h1>
          {subHeaderText && (
            <p className="text-gray-500 text-sm">
              {subHeaderText}
            </p>
          )}
          {/* Results Summary */}
          {totalItems > 0 && (
            <p className="text-gray-500 text-xs mt-1">
              Showing {((currentPage - 1) * itemsPerPage) + 1}-{Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems.toLocaleString()} results
            </p>
          )}
        </div>

        {/* Right side - Filter & Sort */}
        <div className="ml-4 ">
          <EnhancedFilterSort />
        </div>
      </div>

      {/* Results */}
      <div className="space-y-8">
        {/* Shops Section */}
        {shops.length > 0 && (
          <ShopsList
            shops={shops}
            loading={false}
            error={null}
          />
        )}

        {/* Products Section */}
        <ProductsGrid
          products={allProducts}
          loading={false}
          error={null}
          showEmptyState={false}
          hideHeader={true}
        />

        {/* No Results */}
        {allProducts.length === 0 && shops.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-2">No results found</p>
            <p className="text-sm text-gray-400">
              Try adjusting your search or filters
            </p>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <PaginationComponent
            totalPages={totalPages}
            currentPage={currentPage}
          />
        )}
      </div>
    </section>
  );
};

import React, { Suspense } from 'react'
import LottieLoader from '@/components/ui/LottieLoader';

const CatalogPage = () => {
  return (
    <Suspense fallback={
      <section className="w-full mt-16 flex items-center justify-center py-12">
        <LottieLoader
          size="sm"
          text="Loading catalog..."
          textSize="md"
          centered={true}
        />
      </section>
    }>
        <CatalogPageContent/>
    </Suspense>
  )
}


export default CatalogPage;
