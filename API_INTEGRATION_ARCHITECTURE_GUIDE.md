# EveryFash API Integration Architecture Guide

## 🏗️ Complete System Overview

This guide provides a comprehensive walkthrough of how API integration works in the EveryFash system, from the base client to React components, with detailed implementation examples.

## 📋 Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Module 1: Base API Client](#module-1-base-api-client)
3. [Module 2: Authentication Manager](#module-2-authentication-manager)
4. [Module 3: API Endpoints & Configuration](#module-3-api-endpoints--configuration)
5. [Module 4: Error Handling System](#module-4-error-handling-system)
6. [Module 5: Interceptors](#module-5-interceptors)
7. [Module 6: API Functions](#module-6-api-functions)
8. [Module 7: React Query Hooks](#module-7-react-query-hooks)
9. [Module 8: Component Integration](#module-8-component-integration)
10. [Module 9: State Management](#module-9-state-management)
11. [Complete Flow Examples](#complete-flow-examples)

---

## Architecture Overview

```mermaid
graph TD
    A[React Component] --> B[React Query Hook]
    B --> C[API Function]
    C --> D[BaseApiClient]
    D --> E[AuthManager]
    D --> F[Interceptors]
    D --> G[HTTP Request]
    G --> H[Backend API]
    
    H --> I[Response]
    I --> F
    I --> J[Error Handler]
    J --> K[Toast Notification]
    I --> L[Cache Update]
    L --> A
    
    E --> M[localStorage]
    F --> N[Logging]
    F --> O[Performance Monitoring]
```

### Key Principles

1. **Centralized Authentication**: All auth logic in `AuthManager`
2. **Type Safety**: Full TypeScript coverage with strict typing
3. **Error Handling**: Standardized error processing and user feedback
4. **Caching Strategy**: Intelligent React Query caching with deduplication
5. **Interceptors**: Cross-cutting concerns handled transparently
6. **Retry Logic**: Smart retry strategies based on error types

---

## Module 1: Base API Client

**Location**: `lib/api/base-client.ts`

The `BaseApiClient` is the foundation of all API communication. It handles:

### Core Features

```typescript
export class BaseApiClient {
  private baseURL: string;
  private defaultTimeout: number = 30000;
  private defaultRetries: number = 3;

  constructor(baseURL?: string) {
    this.baseURL = baseURL || API_CONFIG.BASE_URL;
  }
}
```

### Request Flow

```typescript
protected async request<T>(
  endpoint: string,
  config: RequestConfig = {}
): Promise<T> {
  const {
    timeout = this.defaultTimeout,
    retries = this.defaultRetries,
    requiresAuth = true,
    isPublic = false,
    ...requestConfig
  } = config;

  // 1. Build URL
  const url = `${this.baseURL}${endpoint}`;
  
  // 2. Build headers with authentication
  const headers: Record<string, string> = {
    ...DEFAULT_HEADERS,
    ...(requestConfig.headers || {}),
  };

  // 3. Add authentication header if required
  if (requiresAuth && !isPublic) {
    const authHeader = AuthManager.getAuthHeader();
    if (authHeader) {
      headers.Authorization = authHeader; // "Bearer <token>"
    }
  }

  // 4. Execute request with retry logic
  return this.executeWithRetry(url, finalConfig, retries, timeout, context);
}
```

### HTTP Method Helpers

```typescript
// All methods automatically include authentication
async get<T>(endpoint: string, config: RequestConfig = {}): Promise<T>
async post<T>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<T>
async put<T>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<T>
async patch<T>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<T>
async delete<T>(endpoint: string, config: RequestConfig = {}): Promise<T>

// Public methods (no authentication)
async publicGet<T>(endpoint: string, config: RequestConfig = {}): Promise<T>
async publicPost<T>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<T>

// FormData methods
async postFormData<T>(endpoint: string, formData: FormData): Promise<T>
async patchFormData<T>(endpoint: string, formData: FormData): Promise<T>
```

---

## Module 2: Authentication Manager

**Location**: `lib/api/auth-manager.ts`

Centralized authentication and session management.

### Token Management

```typescript
export class AuthManager {
  private static readonly TOKEN_KEY = 'auth_token';
  private static readonly TOKEN_INFO_KEY = 'auth_token_info';

  // Store token with metadata
  static setToken(token: string, role: 'buyer' | 'creator'): void {
    const tokenInfo: TokenInfo = {
      token,
      expiresAt: tokenInfo.exp * 1000,
      issuedAt: tokenInfo.iat * 1000,
      role,
    };

    localStorage.setItem(this.TOKEN_KEY, token);
    localStorage.setItem(this.TOKEN_INFO_KEY, JSON.stringify(tokenInfo));
  }

  // Get authorization header
  static getAuthHeader(): string | null {
    const token = this.getToken();
    return token ? `Bearer ${token}` : null;
  }
}
```

### Session Validation

```typescript
static getSessionInfo(): SessionInfo {
  const token = this.getToken();
  const tokenInfo = this.getTokenInfo();

  if (!token || !tokenInfo) {
    return { isValid: false, isExpired: true, expiresIn: 0 };
  }

  const now = Date.now();
  const isExpired = tokenInfo.expiresAt <= now;
  const expiresIn = Math.max(0, Math.floor((tokenInfo.expiresAt - now) / 1000));

  return {
    isValid: !isExpired,
    isExpired,
    expiresIn,
    role: tokenInfo.role,
  };
}
```

---

## Module 3: API Endpoints & Configuration

**Location**: `lib/api/config.ts`

Centralized endpoint and configuration management.

### Endpoint Structure

```typescript
export const API_ENDPOINTS = {
  // Creator products endpoints (require authentication)
  CREATOR_PRODUCTS: {
    LIST: '/creators/products',
    CREATE: '/creators/products',
    DETAIL: '/creators/products', // /{id}
    UPDATE: '/creators/products', // /{id}
    DELETE: '/creators/products', // /{id}
    COUNTS: '/creators/products/counts',
    SPECIFICATIONS: '/creators/products', // /{id}/specifications
    BASIC_INFO: '/creators/products', // /{id}/basic-info
    VARIATIONS: '/creators/products', // /{id}/variations
    UPDATE_VARIATION: '/creators/products', // /{id}/variations/{variationId}
    DELETE_VARIATION: '/creators/products', // /{id}/variations/{variationId}
    IMAGES: '/creators/products', // /{id}/images
  },

  // Public products endpoints (optional authentication)
  PRODUCTS: {
    LIST: '/products',
    DETAIL: '/products', // /{id}
    RELATED: '/buyers/products', // /{id}/related
    SHOP_PRODUCTS: '/buyers/products', // /{id}/shop-products
  },
};
```

### Configuration

```typescript
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://everyfash-api.onrender.com/api/v1',
  TIMEOUT: 30000,
  RETRY_DELAY: 1000,
  MAX_RETRIES: 3,
} as const;

export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
} as const;
```

---

## Module 4: Error Handling System

**Location**: `lib/api/errors.ts`

Standardized error processing and user feedback.

### Error Types

```typescript
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

export class ApiErrorHandler {
  static handleError(error: unknown): ApiError {
    // Network errors
    if (this.isNetworkError(error)) {
      return {
        message: 'Network connection failed',
        code: 'NETWORK_ERROR',
      };
    }

    // HTTP errors with response
    if (this.hasResponse(error)) {
      const response = (error as any).response;
      return {
        message: response.data?.message || 'Request failed',
        status: response.status,
        code: this.getErrorCode(response.status),
        details: response.data,
      };
    }

    // Generic errors
    return {
      message: error instanceof Error ? error.message : 'Unknown error occurred',
      code: 'UNKNOWN_ERROR',
    };
  }
}
```

### Error Classification

```typescript
static isNetworkError(error: ApiError): boolean {
  return error.code === 'NETWORK_ERROR' || 
         error.message.includes('Network') ||
         error.message.includes('fetch');
}

static isAuthenticationError(error: ApiError): boolean {
  return error.status === 401 || 
         error.code === 'UNAUTHORIZED' ||
         error.message.includes('Session expired');
}

static isValidationError(error: ApiError): boolean {
  return error.status === 400 || 
         error.code === 'VALIDATION_ERROR';
}
```

---

## Module 5: Interceptors

**Location**: `lib/api/interceptors.ts`

Cross-cutting concerns handled transparently across all requests.

### Interceptor Types

```typescript
export interface ResponseInterceptor {
  name: string;
  priority: number; // Lower numbers run first
  onResponse?: (response: Response, context: InterceptorContext) => Promise<Response>;
  onError?: (error: Error, context: InterceptorContext) => Promise<Error>;
  onSuccess?: (data: any, context: InterceptorContext) => Promise<any>;
}
```

### Default Interceptors

1. **Logging Interceptor** (Priority: 1)
```typescript
{
  name: 'logger',
  priority: 1,
  onResponse: async (response, context) => {
    const duration = Date.now() - context.startTime;
    console.log(`[API] ${context.method} ${context.url} - ${response.status} (${duration}ms)`);
    return response;
  }
}
```

2. **Authentication Interceptor** (Priority: 2)
```typescript
{
  name: 'auth',
  priority: 2,
  onResponse: async (response, context) => {
    if (response.status === 401 && !isAuthEndpoint(context.url)) {
      AuthManager.handleAuthFailure();
    }
    return response;
  }
}
```

3. **Performance Monitoring** (Priority: 4)
```typescript
{
  name: 'performance',
  priority: 4,
  onResponse: async (response, context) => {
    const duration = Date.now() - context.startTime;
    if (duration > 5000) {
      console.warn(`[API] Slow request: ${context.method} ${context.url} (${duration}ms)`);
    }
    return response;
  }
}
```

---

## Module 6: API Functions

**Location**: `lib/api/products.ts`

Type-safe API function implementations.

### Creator Products API

```typescript
const productsApiBase = {
  // GET /creators/products - List creator's products
  getCreatorProducts: async (params?: {
    page?: number;
    limit?: number;
    sort?: string;
    search?: string;
    status?: string;
    type?: ProductType;
  }) => {
    const queryParams = new URLSearchParams();
    // Build query parameters...
    
    const endpoint = queryString ? 
      `${API_ENDPOINTS.CREATOR_PRODUCTS.LIST}?${queryString}` : 
      API_ENDPOINTS.CREATOR_PRODUCTS.LIST;
    
    return await apiClient.get<ProductsResponse>(endpoint);
    // Authorization: Bearer <token> added automatically
  },

  // POST /creators/products - Create new product
  createProduct: async (data: CreateProductOrBaleData) => {
    const formData = new FormData();
    // Build FormData...
    
    return await apiClient.postFormData<CreateProductResponse>(
      API_ENDPOINTS.CREATOR_PRODUCTS.CREATE, 
      formData
    );
    // Authorization: Bearer <token> added automatically
  },

  // GET /creators/products/{id} - Get single product
  getCreatorProduct: async (productId: string) => {
    return await apiClient.get<ProductDetailResponse>(
      `${API_ENDPOINTS.CREATOR_PRODUCTS.DETAIL}/${productId}`
    );
    // Authorization: Bearer <token> added automatically
  },

  // PATCH /creators/products/{id} - Update product
  updateProduct: async (productId: string, data: Partial<CreateProductData>) => {
    const formData = new FormData();
    // Build FormData with updated fields...
    
    return await apiClient.patchFormData<CreateProductResponse>(
      `${API_ENDPOINTS.CREATOR_PRODUCTS.UPDATE}/${productId}`, 
      formData
    );
    // Authorization: Bearer <token> added automatically
  },

  // DELETE /creators/products/{id} - Delete product
  deleteProduct: async (productId: string) => {
    return await apiClient.delete<{ status: string; message: string }>(
      `${API_ENDPOINTS.CREATOR_PRODUCTS.DELETE}/${productId}`
    );
    // Authorization: Bearer <token> added automatically
  },
};
```

### Request Deduplication

```typescript
// Apply deduplication to frequently called functions
export const productsApi = {
  ...productsApiBase,
  getCreatorProducts: withDeduplication(
    productsApiBase.getCreatorProducts,
    (params) => `getCreatorProducts:${JSON.stringify(params || {})}`
  ),
  getProductCounts: withDeduplication(
    productsApiBase.getProductCounts,
    (params) => `getProductCounts:${JSON.stringify(params || {})}`
  ),
};
```

---

## Module 7: React Query Hooks

**Location**: `lib/hooks/use-products.ts`

React Query integration with intelligent caching and error handling.

### Query Keys Strategy

```typescript
export const productKeys = {
  all: ['products'] as const,
  lists: () => [...productKeys.all, 'list'] as const,
  list: (filters: string) => [...productKeys.lists(), { filters }] as const,
  details: () => [...productKeys.all, 'detail'] as const,
  detail: (id: string) => [...productKeys.details(), id] as const,
};
```

### Query Hooks

```typescript
// Get creator's products with intelligent caching
export const useCreatorProducts = (
  params?: {
    page?: number;
    limit?: number;
    sort?: string;
    search?: string;
    status?: string;
    type?: ProductType;
  },
  options?: { enabled?: boolean }
) => {
  return useQuery({
    queryKey: productKeys.list(JSON.stringify(params || {})),
    queryFn: () => productsApi.getCreatorProducts(params),
    staleTime: 10 * 60 * 1000, // 10 minutes - longer cache
    gcTime: 15 * 60 * 1000, // 15 minutes - keep in cache longer
    refetchOnWindowFocus: false, // Prevent unnecessary refetches
    refetchOnMount: false, // Don't refetch if data exists
    enabled: options?.enabled !== false,
    retry: (failureCount, error: any) => {
      // Smart retry logic
      if (error?.message?.includes('Session expired') || error?.status === 429) {
        return false; // Don't retry auth or rate limit errors
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Get single product with caching
export const useCreatorProduct = (productId: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: [...productKeys.detail(productId), 'creator'],
    queryFn: () => productsApi.getCreatorProduct(productId),
    enabled: !!productId && (options?.enabled !== false),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      if (error?.message?.includes('Session expired') || 
          error?.message?.includes('not found')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};
```

### Mutation Hooks

```typescript
// Create product mutation with cache invalidation
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProductOrBaleData) => productsApi.createProduct(data),
    onSuccess: (_, variables) => {
      // Invalidate all product-related queries
      queryClient.invalidateQueries({ queryKey: productKeys.all });
      
      // Show success toast
      toast({
        title: 'Product Created Successfully',
        description: 'Your product has been created and is pending approval.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      // Show error toast
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    },
  });
};

// Update product mutation with optimistic updates
export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, data }: { productId: string; data: Partial<CreateProductData> }) => 
      productsApi.updateProduct(productId, data),
    onSuccess: (data, variables) => {
      // Update cached product data immediately
      queryClient.setQueryData(productKeys.detail(variables.productId), data);
      
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: productKeys.all });
      queryClient.refetchQueries({ queryKey: productKeys.lists() });
      
      toast({
        title: 'Product Updated Successfully',
        description: 'Your product has been updated successfully.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    },
  });
};
```

### Delete Product Hook

```typescript
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (productId: string) => productsApi.deleteProduct(productId),
    onSuccess: (_, productId) => {
      // Remove from cache optimistically
      queryClient.setQueryData(
        productKeys.lists(),
        (oldData: any) => {
          if (!oldData) return oldData;
          return {
            ...oldData,
            data: {
              ...oldData.data,
              products: oldData.data.products.filter((p: any) => p._id !== productId)
            }
          };
        }
      );

      // Invalidate to ensure consistency
      queryClient.invalidateQueries({ queryKey: productKeys.all });

      toast({
        title: 'Product Deleted',
        description: 'Product has been successfully deleted.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Delete Failed',
        description: getApiErrorMessage(error),
      });
    },
  });
};
```

---

## Module 8: Component Integration

**Location**: `app/creators/(main)/(routes)/products/page.tsx`

How React components use the API hooks.

### Component Implementation

```typescript
"use client";
import React, { useState } from "react";
import { useCreatorProducts, useDeleteProduct, useProductCounts } from "@/lib/hooks/use-products";

export default function ProductsPage() {
  const [activeTab, setActiveTab] = useState<ProductType>('product');
  const [currentPage, setCurrentPage] = useState(1);

  // API hooks with automatic authorization
  const {
    data: productsResponse,
    isLoading: productsLoading,
    error: productsError
  } = useCreatorProducts({
    type: activeTab,
    page: currentPage,
    limit: 10,
    sort: '-createdAt'
  });

  const {
    data: countsResponse,
    isLoading: countsLoading
  } = useProductCounts({ type: activeTab });

  const deleteProductMutation = useDeleteProduct();

  // Handle delete with optimistic updates
  const handleDelete = async (productId: string) => {
    try {
      await deleteProductMutation.mutateAsync(productId);
      // Cache automatically updated by mutation
    } catch (error) {
      // Error automatically handled by mutation
    }
  };

  // Error handling
  useEffect(() => {
    if (productsError) {
      toast({
        variant: "destructive",
        title: "Failed to load products",
        description: "There was an issue loading your products. Please try again.",
      });
    }
  }, [productsError]);

  return (
    <div>
      {/* Loading states handled automatically */}
      {productsLoading && <LottieLoader />}

      {/* Data rendering */}
      {productsResponse?.data?.products?.map(product => (
        <ProductCard
          key={product._id}
          product={product}
          onDelete={() => handleDelete(product._id)}
        />
      ))}
    </div>
  );
}
```

---

## Module 9: State Management Integration

**Location**: `lib/stores/auth-store.ts`

Integration with Zustand for global state management.

### Auth Store Integration

```typescript
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  setUser: (user: User) => void;
  setToken: (token: string) => void;
  clearError: () => void;
}

export const useAuthStore = create<AuthState & AuthActions>((set, get) => ({
  // State
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,

  // Actions
  login: async (credentials) => {
    set({ isLoading: true, error: null });
    try {
      const response = await authApi.login(credentials);

      // Store token in AuthManager
      AuthManager.setToken(response.data.token, response.data.user.role);

      // Update store state
      set({
        user: response.data.user,
        token: response.data.token,
        isAuthenticated: true,
        isLoading: false,
      });
    } catch (error) {
      const apiError = handleApiError(error);
      set({ error: apiError.message, isLoading: false });
      throw error;
    }
  },

  logout: () => {
    AuthManager.clearAuth();
    set({
      user: null,
      token: null,
      isAuthenticated: false,
      error: null,
    });
  },
}));
```

---

## Complete Flow Examples

### Example 1: Creating a Product (Full Flow)

#### 1. Component Initiates Request
```typescript
// Component: ProductCreateForm.tsx
const createProductMutation = useCreateProduct();

const handleSubmit = async (formData: CreateProductData) => {
  try {
    await createProductMutation.mutateAsync(formData);
    router.push('/creators/products');
  } catch (error) {
    // Error handled automatically by mutation
  }
};
```

#### 2. React Query Hook Processes
```typescript
// Hook: use-products.ts
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProductOrBaleData) => productsApi.createProduct(data),
    // Success/error handling...
  });
};
```

#### 3. API Function Executes
```typescript
// API: products.ts
createProduct: async (data: CreateProductOrBaleData) => {
  const formData = new FormData();
  // Build FormData...

  return await apiClient.postFormData<CreateProductResponse>(
    API_ENDPOINTS.CREATOR_PRODUCTS.CREATE,
    formData
  );
}
```

#### 4. BaseApiClient Processes
```typescript
// BaseApiClient: base-client.ts
async postFormData<T>(endpoint: string, formData: FormData): Promise<T> {
  return this.request<T>(endpoint, {
    method: 'POST',
    body: formData,
    headers: {}, // No Content-Type for FormData
  });
}

protected async request<T>(endpoint: string, config: RequestConfig = {}): Promise<T> {
  // 1. Add Authorization header from AuthManager
  if (requiresAuth && !isPublic) {
    const authHeader = AuthManager.getAuthHeader(); // "Bearer <token>"
    if (authHeader) {
      headers.Authorization = authHeader;
    }
  }

  // 2. Execute request with interceptors
  return this.executeWithRetry(url, finalConfig, retries, timeout, context);
}
```

#### 5. Request Execution & Response
```typescript
// BaseApiClient: executeRequest method
const response = await fetch(url, {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    // No Content-Type for FormData
  },
  body: formData,
});

// Process through interceptors
const processedResponse = await interceptorManager.processResponse(response, context);

// Handle authentication errors
if (processedResponse.status === 401) {
  AuthManager.handleAuthFailure();
  throw new Error('Session expired. Please login again.');
}

const data = await processedResponse.json();
return data;
```

#### 6. Success/Error Handling
```typescript
// Back to mutation hook
onSuccess: (data, variables) => {
  // Update cache
  queryClient.invalidateQueries({ queryKey: productKeys.all });

  // Show success toast
  toast({
    title: 'Product Created Successfully',
    description: 'Your product has been created and is pending approval.',
    className: 'bg-green-100 text-green-800',
  });
},
onError: (error) => {
  // Show error toast
  toast({
    variant: 'destructive',
    title: getApiErrorTitle(error),
    description: getApiErrorMessage(error),
  });
}
```

### Example 2: Fetching Products (Full Flow)

#### 1. Component Mounts
```typescript
// Component: ProductsPage.tsx
const {
  data: productsResponse,
  isLoading,
  error
} = useCreatorProducts({
  type: 'product',
  page: 1,
  limit: 10
});
```

#### 2. React Query Checks Cache
```typescript
// React Query internal logic
const cacheKey = productKeys.list(JSON.stringify({ type: 'product', page: 1, limit: 10 }));
// Cache key: ['products', 'list', { filters: '{"type":"product","page":1,"limit":10}' }]

// If cache is fresh (within staleTime), return cached data
// If cache is stale or doesn't exist, execute queryFn
```

#### 3. Query Function Executes
```typescript
// Hook: use-products.ts
queryFn: () => productsApi.getCreatorProducts({
  type: 'product',
  page: 1,
  limit: 10
})
```

#### 4. API Function Builds Request
```typescript
// API: products.ts
getCreatorProducts: async (params) => {
  const queryParams = new URLSearchParams();
  queryParams.append('type', 'product');
  queryParams.append('page', '1');
  queryParams.append('limit', '10');

  const endpoint = `/creators/products?type=product&page=1&limit=10`;

  return await apiClient.get<ProductsResponse>(endpoint);
}
```

#### 5. BaseApiClient Executes
```typescript
// BaseApiClient: base-client.ts
async get<T>(endpoint: string, config: RequestConfig = {}): Promise<T> {
  return this.request<T>(endpoint, { ...config, method: 'GET' });
}

// In request method:
// 1. Add Authorization: Bearer <token>
// 2. Execute fetch with interceptors
// 3. Handle response/errors
// 4. Return typed data
```

#### 6. Component Receives Data
```typescript
// Component automatically re-renders with:
// - productsResponse.data.products (array of products)
// - isLoading: false
// - error: null (if successful)

// Component renders products
{productsResponse?.data?.products?.map(product => (
  <ProductCard key={product._id} product={product} />
))}
```

---

## 🔄 Request/Response Flow Summary

```
1. Component calls hook
   ↓
2. Hook checks React Query cache
   ↓
3. If cache miss, hook calls API function
   ↓
4. API function builds request parameters
   ↓
5. BaseApiClient adds authentication headers
   ↓
6. Interceptors process request
   ↓
7. HTTP request sent to backend
   ↓
8. Response received
   ↓
9. Interceptors process response
   ↓
10. Error handling (if needed)
   ↓
11. Data returned to hook
   ↓
12. React Query updates cache
   ↓
13. Component re-renders with new data
```

---

## 🎯 Key Benefits

1. **Automatic Authentication**: All creator endpoints get Bearer tokens automatically
2. **Type Safety**: Full TypeScript coverage prevents runtime errors
3. **Intelligent Caching**: React Query prevents redundant API calls
4. **Error Resilience**: Automatic retries with exponential backoff
5. **User Feedback**: Consistent toast notifications for all operations
6. **Performance Monitoring**: Automatic logging of slow requests
7. **Session Management**: Automatic logout on token expiration
8. **Request Deduplication**: Prevents duplicate concurrent requests

This architecture ensures robust, scalable, and maintainable API integration across the entire EveryFash platform.
```
