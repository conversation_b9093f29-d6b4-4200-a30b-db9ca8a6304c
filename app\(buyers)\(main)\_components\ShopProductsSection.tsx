"use client";

import React from 'react';
import ProductsCarousel from './ProductsCarousel';
import ProductCard from '@/components/ui/custom/ProductCard';
import ProductCarouselSkeleton from './ProductCarouselSkeleton';
import { useBuyerShopProducts } from '@/lib/hooks/use-buyer-products';
import { useAuth } from '@/lib/stores/auth-store';

interface ShopProductsSectionProps {
  productId: string;
  title?: string;
  limit?: number;
}

const ShopProductsSection: React.FC<ShopProductsSectionProps> = ({ 
  productId, 
  title = "You may also like",
  limit = 8
}) => {
  const { isAuthenticated } = useAuth();
  
  // Only fetch if user is authenticated
  const { data: shopResponse, isLoading, error } = useBuyerShopProducts(
    productId,
    { page: 1, limit },
    { enabled: isAuthenticated }
  );

  // Don't render anything if user is not authenticated
  if (!isAuthenticated) {
    return null;
  }

  // Show skeleton while loading
  if (isLoading) {
    return <ProductCarouselSkeleton title={title} itemCount={4} />;
  }

  // Don't show anything if there's an error or no products
  if (error || !shopResponse?.data?.products?.length) {
    return null;
  }

  const shopProducts = shopResponse.data.products;

  return (
    <section className='bg-white my-2 py-3 px-3 rounded shadow-sm'>
      <h1 className="text-sm font-semibold text-gray-600 pb-2">{title}</h1>
      <ProductsCarousel>
        {shopProducts.map((product) => (
          <div key={product._id} className="p-1">
            <ProductCard
              product={{
                id: product._id || '',
                name: product.name || '',
                image: (product.images && product.images[0]) || '/placeholder-image.jpg',
                price: product.formattedPriceRange?.discounted?.min || product.formattedPriceRange?.original?.min || 0,
                discount: product.maxDiscountPercentage || 0,
                itemsLeft: product.totalItemsLeft || 0,
                availableColors: product.availableColors || [],
                availableSizes: product.availableSizes || [],
                availableIdentifiers: product.availableIdentifiers || [],
                type: product.type || 'product',
              }}
              cardType='normal'
            />
          </div>
        ))}
      </ProductsCarousel>
    </section>
  );
};

export default ShopProductsSection;
