# EveryFash API Integration Architecture Guide

## 🏗️ Complete System Overview

This guide provides a comprehensive walkthrough of how API integration works in the EveryFash system, from the base client to React components, with detailed implementation examples.

## 📋 Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Module 1: Base API Client](#module-1-base-api-client)
3. [Module 2: Authentication Manager](#module-2-authentication-manager)
4. [Module 3: API Endpoints & Configuration](#module-3-api-endpoints--configuration)
5. [Module 4: Error Handling System](#module-4-error-handling-system)
6. [Module 5: Interceptors](#module-5-interceptors)
7. [Module 6: API Functions](#module-6-api-functions)
8. [Module 7: React Query Hooks](#module-7-react-query-hooks)
9. [Module 8: Component Integration](#module-8-component-integration)
10. [Module 9: State Management](#module-9-state-management)
11. [Complete Flow Examples](#complete-flow-examples)

---

## Architecture Overview

```mermaid
graph TD
    A[React Component] --> B[React Query Hook]
    B --> C[API Function]
    C --> D[BaseApiClient]
    D --> E[AuthManager]
    D --> F[Interceptors]
    D --> G[HTTP Request]
    G --> H[Backend API]
    
    H --> I[Response]
    I --> F
    I --> J[Error Handler]
    J --> K[Toast Notification]
    I --> L[Cache Update]
    L --> A
    
    E --> M[localStorage]
    F --> N[Logging]
    F --> O[Performance Monitoring]
```

### Key Principles

1. **Centralized Authentication**: All auth logic in `AuthManager`
2. **Type Safety**: Full TypeScript coverage with strict typing
3. **Error Handling**: Standardized error processing and user feedback
4. **Caching Strategy**: Intelligent React Query caching with deduplication
5. **Interceptors**: Cross-cutting concerns handled transparently
6. **Retry Logic**: Smart retry strategies based on error types

---

## Module 1: Base API Client

**Location**: `lib/api/base-client.ts`

The `BaseApiClient` is the foundation of all API communication. It handles:

### Core Features

```typescript
export class BaseApiClient {
  private baseURL: string;
  private defaultTimeout: number = 30000;
  private defaultRetries: number = 3;

  constructor(baseURL?: string) {
    this.baseURL = baseURL || API_CONFIG.BASE_URL;
  }
}
```

### Request Flow

```typescript
protected async request<T>(
  endpoint: string,
  config: RequestConfig = {}
): Promise<T> {
  const {
    timeout = this.defaultTimeout,
    retries = this.defaultRetries,
    requiresAuth = true,
    isPublic = false,
    ...requestConfig
  } = config;

  // 1. Build URL
  const url = `${this.baseURL}${endpoint}`;
  
  // 2. Build headers with authentication
  const headers: Record<string, string> = {
    ...DEFAULT_HEADERS,
    ...(requestConfig.headers || {}),
  };

  // 3. Add authentication header if required
  if (requiresAuth && !isPublic) {
    const authHeader = AuthManager.getAuthHeader();
    if (authHeader) {
      headers.Authorization = authHeader; // "Bearer <token>"
    }
  }

  // 4. Execute request with retry logic
  return this.executeWithRetry(url, finalConfig, retries, timeout, context);
}
```

### HTTP Method Helpers

```typescript
// All methods automatically include authentication
async get<T>(endpoint: string, config: RequestConfig = {}): Promise<T>
async post<T>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<T>
async put<T>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<T>
async patch<T>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<T>
async delete<T>(endpoint: string, config: RequestConfig = {}): Promise<T>

// Public methods (no authentication)
async publicGet<T>(endpoint: string, config: RequestConfig = {}): Promise<T>
async publicPost<T>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<T>

// FormData methods
async postFormData<T>(endpoint: string, formData: FormData): Promise<T>
async patchFormData<T>(endpoint: string, formData: FormData): Promise<T>
```

---

## Module 2: Authentication Manager

**Location**: `lib/api/auth-manager.ts`

Centralized authentication and session management.

### Token Management

```typescript
export class AuthManager {
  private static readonly TOKEN_KEY = 'auth_token';
  private static readonly TOKEN_INFO_KEY = 'auth_token_info';

  // Store token with metadata
  static setToken(token: string, role: 'buyer' | 'creator'): void {
    const tokenInfo: TokenInfo = {
      token,
      expiresAt: tokenInfo.exp * 1000,
      issuedAt: tokenInfo.iat * 1000,
      role,
    };

    localStorage.setItem(this.TOKEN_KEY, token);
    localStorage.setItem(this.TOKEN_INFO_KEY, JSON.stringify(tokenInfo));
  }

  // Get authorization header
  static getAuthHeader(): string | null {
    const token = this.getToken();
    return token ? `Bearer ${token}` : null;
  }
}
```

### Session Validation

```typescript
static getSessionInfo(): SessionInfo {
  const token = this.getToken();
  const tokenInfo = this.getTokenInfo();

  if (!token || !tokenInfo) {
    return { isValid: false, isExpired: true, expiresIn: 0 };
  }

  const now = Date.now();
  const isExpired = tokenInfo.expiresAt <= now;
  const expiresIn = Math.max(0, Math.floor((tokenInfo.expiresAt - now) / 1000));

  return {
    isValid: !isExpired,
    isExpired,
    expiresIn,
    role: tokenInfo.role,
  };
}
```

---

## Module 3: API Endpoints & Configuration

**Location**: `lib/api/config.ts`

Centralized endpoint and configuration management.

### Endpoint Structure

```typescript
export const API_ENDPOINTS = {
  // Creator products endpoints (require authentication)
  CREATOR_PRODUCTS: {
    LIST: '/creators/products',
    CREATE: '/creators/products',
    DETAIL: '/creators/products', // /{id}
    UPDATE: '/creators/products', // /{id}
    DELETE: '/creators/products', // /{id}
    COUNTS: '/creators/products/counts',
    SPECIFICATIONS: '/creators/products', // /{id}/specifications
    BASIC_INFO: '/creators/products', // /{id}/basic-info
    VARIATIONS: '/creators/products', // /{id}/variations
    UPDATE_VARIATION: '/creators/products', // /{id}/variations/{variationId}
    DELETE_VARIATION: '/creators/products', // /{id}/variations/{variationId}
    IMAGES: '/creators/products', // /{id}/images
  },

  // Public products endpoints (optional authentication)
  PRODUCTS: {
    LIST: '/products',
    DETAIL: '/products', // /{id}
    RELATED: '/buyers/products', // /{id}/related
    SHOP_PRODUCTS: '/buyers/products', // /{id}/shop-products
  },
};
```

### Configuration

```typescript
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://everyfash-api.onrender.com/api/v1',
  TIMEOUT: 30000,
  RETRY_DELAY: 1000,
  MAX_RETRIES: 3,
} as const;

export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
} as const;
```

---

## Module 4: Error Handling System

**Location**: `lib/api/errors.ts`

Standardized error processing and user feedback.

### Error Types

```typescript
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

export class ApiErrorHandler {
  static handleError(error: unknown): ApiError {
    // Network errors
    if (this.isNetworkError(error)) {
      return {
        message: 'Network connection failed',
        code: 'NETWORK_ERROR',
      };
    }

    // HTTP errors with response
    if (this.hasResponse(error)) {
      const response = (error as any).response;
      return {
        message: response.data?.message || 'Request failed',
        status: response.status,
        code: this.getErrorCode(response.status),
        details: response.data,
      };
    }

    // Generic errors
    return {
      message: error instanceof Error ? error.message : 'Unknown error occurred',
      code: 'UNKNOWN_ERROR',
    };
  }
}
```

### Error Classification

```typescript
static isNetworkError(error: ApiError): boolean {
  return error.code === 'NETWORK_ERROR' || 
         error.message.includes('Network') ||
         error.message.includes('fetch');
}

static isAuthenticationError(error: ApiError): boolean {
  return error.status === 401 || 
         error.code === 'UNAUTHORIZED' ||
         error.message.includes('Session expired');
}

static isValidationError(error: ApiError): boolean {
  return error.status === 400 || 
         error.code === 'VALIDATION_ERROR';
}
```

---

## Module 5: Interceptors

**Location**: `lib/api/interceptors.ts`

Cross-cutting concerns handled transparently across all requests.

### Interceptor Types

```typescript
export interface ResponseInterceptor {
  name: string;
  priority: number; // Lower numbers run first
  onResponse?: (response: Response, context: InterceptorContext) => Promise<Response>;
  onError?: (error: Error, context: InterceptorContext) => Promise<Error>;
  onSuccess?: (data: any, context: InterceptorContext) => Promise<any>;
}
```

### Default Interceptors

1. **Logging Interceptor** (Priority: 1)
```typescript
{
  name: 'logger',
  priority: 1,
  onResponse: async (response, context) => {
    const duration = Date.now() - context.startTime;
    console.log(`[API] ${context.method} ${context.url} - ${response.status} (${duration}ms)`);
    return response;
  }
}
```

2. **Authentication Interceptor** (Priority: 2)
```typescript
{
  name: 'auth',
  priority: 2,
  onResponse: async (response, context) => {
    if (response.status === 401 && !isAuthEndpoint(context.url)) {
      AuthManager.handleAuthFailure();
    }
    return response;
  }
}
```

3. **Performance Monitoring** (Priority: 4)
```typescript
{
  name: 'performance',
  priority: 4,
  onResponse: async (response, context) => {
    const duration = Date.now() - context.startTime;
    if (duration > 5000) {
      console.warn(`[API] Slow request: ${context.method} ${context.url} (${duration}ms)`);
    }
    return response;
  }
}
```

---

## Module 6: API Functions

**Location**: `lib/api/products.ts`

Type-safe API function implementations.

### Creator Products API

```typescript
const productsApiBase = {
  // GET /creators/products - List creator's products
  getCreatorProducts: async (params?: {
    page?: number;
    limit?: number;
    sort?: string;
    search?: string;
    status?: string;
    type?: ProductType;
  }) => {
    const queryParams = new URLSearchParams();
    // Build query parameters...
    
    const endpoint = queryString ? 
      `${API_ENDPOINTS.CREATOR_PRODUCTS.LIST}?${queryString}` : 
      API_ENDPOINTS.CREATOR_PRODUCTS.LIST;
    
    return await apiClient.get<ProductsResponse>(endpoint);
    // Authorization: Bearer <token> added automatically
  },

  // POST /creators/products - Create new product
  createProduct: async (data: CreateProductOrBaleData) => {
    const formData = new FormData();
    // Build FormData...
    
    return await apiClient.postFormData<CreateProductResponse>(
      API_ENDPOINTS.CREATOR_PRODUCTS.CREATE, 
      formData
    );
    // Authorization: Bearer <token> added automatically
  },

  // GET /creators/products/{id} - Get single product
  getCreatorProduct: async (productId: string) => {
    return await apiClient.get<ProductDetailResponse>(
      `${API_ENDPOINTS.CREATOR_PRODUCTS.DETAIL}/${productId}`
    );
    // Authorization: Bearer <token> added automatically
  },

  // PATCH /creators/products/{id} - Update product
  updateProduct: async (productId: string, data: Partial<CreateProductData>) => {
    const formData = new FormData();
    // Build FormData with updated fields...
    
    return await apiClient.patchFormData<CreateProductResponse>(
      `${API_ENDPOINTS.CREATOR_PRODUCTS.UPDATE}/${productId}`, 
      formData
    );
    // Authorization: Bearer <token> added automatically
  },

  // DELETE /creators/products/{id} - Delete product
  deleteProduct: async (productId: string) => {
    return await apiClient.delete<{ status: string; message: string }>(
      `${API_ENDPOINTS.CREATOR_PRODUCTS.DELETE}/${productId}`
    );
    // Authorization: Bearer <token> added automatically
  },
};
```

### Complete Creator Products API Implementation

```typescript
const productsApiBase = {
  // LIST: GET /creators/products
  getCreatorProducts: async (params?: {
    page?: number;
    limit?: number;
    sort?: string;
    search?: string;
    status?: string;
    type?: ProductType;
  }) => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.sort) queryParams.append('sort', params.sort);
    if (params?.search) queryParams.append('search', params.search);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.type) queryParams.append('type', params.type);

    const endpoint = queryParams.toString() ?
      `${API_ENDPOINTS.CREATOR_PRODUCTS.LIST}?${queryParams.toString()}` :
      API_ENDPOINTS.CREATOR_PRODUCTS.LIST;

    return await apiClient.get<ProductsResponse>(endpoint);
    // Authorization: Bearer <token> added automatically
  },

  // CREATE: POST /creators/products
  createProduct: async (data: CreateProductOrBaleData) => {
    const formData = new FormData();
    formData.append('type', data.type);
    formData.append('name', data.name);
    formData.append('description', data.description);
    // ... build complete FormData

    return await apiClient.postFormData<CreateProductResponse>(
      API_ENDPOINTS.CREATOR_PRODUCTS.CREATE,
      formData
    );
    // Authorization: Bearer <token> added automatically
  },

  // DETAIL: GET /creators/products/{id}
  getCreatorProduct: async (productId: string) => {
    return await apiClient.get<ProductDetailResponse>(
      `${API_ENDPOINTS.CREATOR_PRODUCTS.DETAIL}/${productId}`
    );
    // Authorization: Bearer <token> added automatically
  },

  // UPDATE: PATCH /creators/products/{id}
  updateProduct: async (productId: string, data: Partial<CreateProductData>) => {
    const formData = new FormData();
    // Only append fields that are being updated
    if (data.name) formData.append('name', data.name);
    if (data.description) formData.append('description', data.description);
    // ... other fields

    return await apiClient.patchFormData<CreateProductResponse>(
      `${API_ENDPOINTS.CREATOR_PRODUCTS.UPDATE}/${productId}`,
      formData
    );
    // Authorization: Bearer <token> added automatically
  },

  // DELETE: DELETE /creators/products/{id}
  deleteProduct: async (productId: string) => {
    return await apiClient.delete<{ status: string; message: string }>(
      `${API_ENDPOINTS.CREATOR_PRODUCTS.DELETE}/${productId}`
    );
    // Authorization: Bearer <token> added automatically
  },

  // COUNTS: GET /creators/products/counts
  getProductCounts: async (params?: { type?: ProductType }) => {
    const queryParams = new URLSearchParams();
    if (params?.type) queryParams.append('type', params.type);

    const endpoint = queryParams.toString() ?
      `${API_ENDPOINTS.CREATOR_PRODUCTS.COUNTS}?${queryParams.toString()}` :
      API_ENDPOINTS.CREATOR_PRODUCTS.COUNTS;

    return await apiClient.get<ProductCountsResponse>(endpoint);
    // Authorization: Bearer <token> added automatically
  },

  // SPECIFICATIONS: PATCH /creators/products/{id}/specifications
  updateProductSpecifications: async (productId: string, specifications: {
    mainMaterial?: string;
    dressStyle?: string;
    pantType?: string;
    // ... other specification fields
  }) => {
    return await apiClient.patch<{ status: string; data: { product: Product } }>(
      `${API_ENDPOINTS.CREATOR_PRODUCTS.SPECIFICATIONS}/${productId}/specifications`,
      specifications
    );
    // Authorization: Bearer <token> added automatically
  },

  // BASIC_INFO: PATCH /creators/products/{id}/basic-info
  updateProductBasicInfo: async (productId: string, basicInfo: {
    name?: string;
    brand?: string;
    description?: string;
    gender?: string;
    basePrice?: number;
    // ... other basic info fields
  }) => {
    return await apiClient.patch<{ status: string; data: { product: Product } }>(
      `${API_ENDPOINTS.CREATOR_PRODUCTS.BASIC_INFO}/${productId}/basic-info`,
      basicInfo
    );
    // Authorization: Bearer <token> added automatically
  },

  // VARIATIONS: PATCH /creators/products/{id}/variations
  updateProductVariations: async (productId: string, data: {
    variations: Array<{
      _id?: string; // Include for existing, omit for new
      color?: string;
      size?: string;
      quantity?: number;
      price?: number;
      salePrice?: number;
      saleEndDate?: string;
    }>;
    relatedCategories?: string[];
  }) => {
    return await apiClient.patch<{ status: string; data: { product: Product } }>(
      `${API_ENDPOINTS.CREATOR_PRODUCTS.VARIATIONS}/${productId}/variations`,
      data
    );
    // Authorization: Bearer <token> added automatically
  },

  // UPDATE_VARIATION: PATCH /creators/products/{id}/variations/{variationId}
  updateProductVariation: async (productId: string, variationId: string, variation: {
    color?: string;
    size?: string;
    quantity?: number;
    price?: number;
    salePrice?: number;
    saleEndDate?: string;
  }) => {
    return await apiClient.patch<{ status: string; data: { variation: ProductVariation } }>(
      `${API_ENDPOINTS.CREATOR_PRODUCTS.UPDATE_VARIATION}/${productId}/variations/${variationId}`,
      variation
    );
    // Authorization: Bearer <token> added automatically
  },

  // DELETE_VARIATION: DELETE /creators/products/{id}/variations/{variationId}
  deleteProductVariation: async (productId: string, variationId: string) => {
    return await apiClient.delete<{ status: string; message: string }>(
      `${API_ENDPOINTS.CREATOR_PRODUCTS.DELETE_VARIATION}/${productId}/variations/${variationId}`
    );
    // Authorization: Bearer <token> added automatically
  },

  // IMAGES: PATCH /creators/products/{id}/images
  updateProductImages: async (productId: string, data: {
    images?: string[]; // Existing image URLs to keep
    productImages?: File[]; // New images to upload
  }) => {
    const formData = new FormData();

    // Add existing images to keep
    if (data.images) {
      data.images.forEach((imageUrl, index) => {
        formData.append(`images[${index}]`, imageUrl);
      });
    }

    // Add new images
    if (data.productImages) {
      data.productImages.forEach((image) => {
        formData.append('productImages', image);
      });
    }

    return await apiClient.patchFormData<{ status: string; message: string; data: { images: string[] } }>(
      `${API_ENDPOINTS.CREATOR_PRODUCTS.IMAGES}/${productId}/images`,
      formData
    );
    // Authorization: Bearer <token> added automatically
  },
};
```

### Request Deduplication

```typescript
// Apply deduplication to frequently called functions
export const productsApi = {
  ...productsApiBase,
  getCreatorProducts: withDeduplication(
    productsApiBase.getCreatorProducts,
    (params) => `getCreatorProducts:${JSON.stringify(params || {})}`
  ),
  getProductCounts: withDeduplication(
    productsApiBase.getProductCounts,
    (params) => `getProductCounts:${JSON.stringify(params || {})}`
  ),
  getCreatorProduct: withDeduplication(
    productsApiBase.getCreatorProduct,
    (productId) => `getCreatorProduct:${productId}`
  ),
};
```

---

## Module 7: React Query Hooks

**Location**: `lib/hooks/use-products.ts`

React Query integration with intelligent caching and error handling.

### Query Keys Strategy

```typescript
export const productKeys = {
  all: ['products'] as const,
  lists: () => [...productKeys.all, 'list'] as const,
  list: (filters: string) => [...productKeys.lists(), { filters }] as const,
  details: () => [...productKeys.all, 'detail'] as const,
  detail: (id: string) => [...productKeys.details(), id] as const,
};
```

### Query Hooks

```typescript
// Get creator's products with intelligent caching
export const useCreatorProducts = (
  params?: {
    page?: number;
    limit?: number;
    sort?: string;
    search?: string;
    status?: string;
    type?: ProductType;
  },
  options?: { enabled?: boolean }
) => {
  return useQuery({
    queryKey: productKeys.list(JSON.stringify(params || {})),
    queryFn: () => productsApi.getCreatorProducts(params),
    staleTime: 10 * 60 * 1000, // 10 minutes - longer cache
    gcTime: 15 * 60 * 1000, // 15 minutes - keep in cache longer
    refetchOnWindowFocus: false, // Prevent unnecessary refetches
    refetchOnMount: false, // Don't refetch if data exists
    enabled: options?.enabled !== false,
    retry: (failureCount, error: any) => {
      // Smart retry logic
      if (error?.message?.includes('Session expired') || error?.status === 429) {
        return false; // Don't retry auth or rate limit errors
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Get single product with caching
export const useCreatorProduct = (productId: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: [...productKeys.detail(productId), 'creator'],
    queryFn: () => productsApi.getCreatorProduct(productId),
    enabled: !!productId && (options?.enabled !== false),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      if (error?.message?.includes('Session expired') || 
          error?.message?.includes('not found')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};
```

### Mutation Hooks

```typescript
// Create product mutation with cache invalidation
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProductOrBaleData) => productsApi.createProduct(data),
    onSuccess: (_, variables) => {
      // Invalidate all product-related queries
      queryClient.invalidateQueries({ queryKey: productKeys.all });
      
      // Show success toast
      toast({
        title: 'Product Created Successfully',
        description: 'Your product has been created and is pending approval.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      // Show error toast
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    },
  });
};

// Update product mutation with optimistic updates
export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, data }: { productId: string; data: Partial<CreateProductData> }) => 
      productsApi.updateProduct(productId, data),
    onSuccess: (data, variables) => {
      // Update cached product data immediately
      queryClient.setQueryData(productKeys.detail(variables.productId), data);
      
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: productKeys.all });
      queryClient.refetchQueries({ queryKey: productKeys.lists() });
      
      toast({
        title: 'Product Updated Successfully',
        description: 'Your product has been updated successfully.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    },
  });
};
```

### Delete Product Hook

```typescript
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (productId: string) => productsApi.deleteProduct(productId),
    onSuccess: (_, productId) => {
      // Remove from cache optimistically
      queryClient.setQueryData(
        productKeys.lists(),
        (oldData: any) => {
          if (!oldData) return oldData;
          return {
            ...oldData,
            data: {
              ...oldData.data,
              products: oldData.data.products.filter((p: any) => p._id !== productId)
            }
          };
        }
      );

      // Invalidate to ensure consistency
      queryClient.invalidateQueries({ queryKey: productKeys.all });

      toast({
        title: 'Product Deleted',
        description: 'Product has been successfully deleted.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Delete Failed',
        description: getApiErrorMessage(error),
      });
    },
  });
};
```

### Delete Product Hook

```typescript
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (productId: string) => productsApi.deleteProduct(productId),
    onSuccess: (_, productId) => {
      // Remove from cache optimistically
      queryClient.setQueryData(
        productKeys.lists(),
        (oldData: any) => {
          if (!oldData) return oldData;
          return {
            ...oldData,
            data: {
              ...oldData.data,
              products: oldData.data.products.filter((p: any) => p._id !== productId)
            }
          };
        }
      );

      // Invalidate to ensure consistency
      queryClient.invalidateQueries({ queryKey: productKeys.all });

      toast({
        title: 'Product Deleted',
        description: 'Product has been successfully deleted.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Delete Failed',
        description: getApiErrorMessage(error),
      });
    },
  });
};
```

---

## Module 8: Component Integration

**Location**: `app/creators/(main)/(routes)/products/page.tsx`

How React components use the API hooks.

### Component Implementation

```typescript
"use client";
import React, { useState } from "react";
import { useCreatorProducts, useDeleteProduct, useProductCounts } from "@/lib/hooks/use-products";

export default function ProductsPage() {
  const [activeTab, setActiveTab] = useState<ProductType>('product');
  const [currentPage, setCurrentPage] = useState(1);

  // API hooks with automatic authorization
  const {
    data: productsResponse,
    isLoading: productsLoading,
    error: productsError
  } = useCreatorProducts({
    type: activeTab,
    page: currentPage,
    limit: 10,
    sort: '-createdAt'
  });

  const {
    data: countsResponse,
    isLoading: countsLoading
  } = useProductCounts({ type: activeTab });

  const deleteProductMutation = useDeleteProduct();

  // Handle delete with optimistic updates
  const handleDelete = async (productId: string) => {
    try {
      await deleteProductMutation.mutateAsync(productId);
      // Cache automatically updated by mutation
    } catch (error) {
      // Error automatically handled by mutation
    }
  };

  // Error handling
  useEffect(() => {
    if (productsError) {
      toast({
        variant: "destructive",
        title: "Failed to load products",
        description: "There was an issue loading your products. Please try again.",
      });
    }
  }, [productsError]);

  return (
    <div>
      {/* Loading states handled automatically */}
      {productsLoading && <LottieLoader />}

      {/* Data rendering */}
      {productsResponse?.data?.products?.map(product => (
        <ProductCard
          key={product._id}
          product={product}
          onDelete={() => handleDelete(product._id)}
        />
      ))}
    </div>
  );
}
```

---

## Module 9: State Management Integration

**Location**: `lib/stores/auth-store.ts`

Integration with Zustand for global state management.

### Auth Store Integration

```typescript
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  setUser: (user: User) => void;
  setToken: (token: string) => void;
  clearError: () => void;
}

export const useAuthStore = create<AuthState & AuthActions>((set, get) => ({
  // State
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,

  // Actions
  login: async (credentials) => {
    set({ isLoading: true, error: null });
    try {
      const response = await authApi.login(credentials);

      // Store token in AuthManager
      AuthManager.setToken(response.data.token, response.data.user.role);

      // Update store state
      set({
        user: response.data.user,
        token: response.data.token,
        isAuthenticated: true,
        isLoading: false,
      });
    } catch (error) {
      const apiError = handleApiError(error);
      set({ error: apiError.message, isLoading: false });
      throw error;
    }
  },

  logout: () => {
    AuthManager.clearAuth();
    set({
      user: null,
      token: null,
      isAuthenticated: false,
      error: null,
    });
  },
}));
```

---

## Complete Flow Examples

### Example 1: Creating a Product (Full Flow)

#### 1. Component Initiates Request
```typescript
// Component: ProductCreateForm.tsx
const createProductMutation = useCreateProduct();

const handleSubmit = async (formData: CreateProductData) => {
  try {
    await createProductMutation.mutateAsync(formData);
    router.push('/creators/products');
  } catch (error) {
    // Error handled automatically by mutation
  }
};
```

#### 2. React Query Hook Processes
```typescript
// Hook: use-products.ts
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProductOrBaleData) => productsApi.createProduct(data),
    // Success/error handling...
  });
};
```

#### 3. API Function Executes
```typescript
// API: products.ts
createProduct: async (data: CreateProductOrBaleData) => {
  const formData = new FormData();
  // Build FormData...

  return await apiClient.postFormData<CreateProductResponse>(
    API_ENDPOINTS.CREATOR_PRODUCTS.CREATE,
    formData
  );
}
```

#### 4. BaseApiClient Processes
```typescript
// BaseApiClient: base-client.ts
async postFormData<T>(endpoint: string, formData: FormData): Promise<T> {
  return this.request<T>(endpoint, {
    method: 'POST',
    body: formData,
    headers: {}, // No Content-Type for FormData
  });
}

protected async request<T>(endpoint: string, config: RequestConfig = {}): Promise<T> {
  // 1. Add Authorization header from AuthManager
  if (requiresAuth && !isPublic) {
    const authHeader = AuthManager.getAuthHeader(); // "Bearer <token>"
    if (authHeader) {
      headers.Authorization = authHeader;
    }
  }

  // 2. Execute request with interceptors
  return this.executeWithRetry(url, finalConfig, retries, timeout, context);
}
```

#### 5. Request Execution & Response
```typescript
// BaseApiClient: executeRequest method
const response = await fetch(url, {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    // No Content-Type for FormData
  },
  body: formData,
});

// Process through interceptors
const processedResponse = await interceptorManager.processResponse(response, context);

// Handle authentication errors
if (processedResponse.status === 401) {
  AuthManager.handleAuthFailure();
  throw new Error('Session expired. Please login again.');
}

const data = await processedResponse.json();
return data;
```

#### 6. Success/Error Handling
```typescript
// Back to mutation hook
onSuccess: (data, variables) => {
  // Update cache
  queryClient.invalidateQueries({ queryKey: productKeys.all });

  // Show success toast
  toast({
    title: 'Product Created Successfully',
    description: 'Your product has been created and is pending approval.',
    className: 'bg-green-100 text-green-800',
  });
},
onError: (error) => {
  // Show error toast
  toast({
    variant: 'destructive',
    title: getApiErrorTitle(error),
    description: getApiErrorMessage(error),
  });
}
```

### Example 2: Fetching Products (Full Flow)

#### 1. Component Mounts
```typescript
// Component: ProductsPage.tsx
const {
  data: productsResponse,
  isLoading,
  error
} = useCreatorProducts({
  type: 'product',
  page: 1,
  limit: 10
});
```

#### 2. React Query Checks Cache
```typescript
// React Query internal logic
const cacheKey = productKeys.list(JSON.stringify({ type: 'product', page: 1, limit: 10 }));
// Cache key: ['products', 'list', { filters: '{"type":"product","page":1,"limit":10}' }]

// If cache is fresh (within staleTime), return cached data
// If cache is stale or doesn't exist, execute queryFn
```

#### 3. Query Function Executes
```typescript
// Hook: use-products.ts
queryFn: () => productsApi.getCreatorProducts({
  type: 'product',
  page: 1,
  limit: 10
})
```

#### 4. API Function Builds Request
```typescript
// API: products.ts
getCreatorProducts: async (params) => {
  const queryParams = new URLSearchParams();
  queryParams.append('type', 'product');
  queryParams.append('page', '1');
  queryParams.append('limit', '10');

  const endpoint = `/creators/products?type=product&page=1&limit=10`;

  return await apiClient.get<ProductsResponse>(endpoint);
}
```

#### 5. BaseApiClient Executes
```typescript
// BaseApiClient: base-client.ts
async get<T>(endpoint: string, config: RequestConfig = {}): Promise<T> {
  return this.request<T>(endpoint, { ...config, method: 'GET' });
}

// In request method:
// 1. Add Authorization: Bearer <token>
// 2. Execute fetch with interceptors
// 3. Handle response/errors
// 4. Return typed data
```

#### 6. Component Receives Data
```typescript
// Component automatically re-renders with:
// - productsResponse.data.products (array of products)
// - isLoading: false
// - error: null (if successful)

// Component renders products
{productsResponse?.data?.products?.map(product => (
  <ProductCard key={product._id} product={product} />
))}
```

---

## 🔄 Request/Response Flow Summary

```
1. Component calls hook
   ↓
2. Hook checks React Query cache
   ↓
3. If cache miss, hook calls API function
   ↓
4. API function builds request parameters
   ↓
5. BaseApiClient adds authentication headers
   ↓
6. Interceptors process request
   ↓
7. HTTP request sent to backend
   ↓
8. Response received
   ↓
9. Interceptors process response
   ↓
10. Error handling (if needed)
   ↓
11. Data returned to hook
   ↓
12. React Query updates cache
   ↓
13. Component re-renders with new data
```

---

## 🎯 Key Benefits

1. **Automatic Authentication**: All creator endpoints get Bearer tokens automatically
2. **Type Safety**: Full TypeScript coverage prevents runtime errors
3. **Intelligent Caching**: React Query prevents redundant API calls
4. **Error Resilience**: Automatic retries with exponential backoff
5. **User Feedback**: Consistent toast notifications for all operations
6. **Performance Monitoring**: Automatic logging of slow requests
7. **Session Management**: Automatic logout on token expiration
8. **Request Deduplication**: Prevents duplicate concurrent requests

This architecture ensures robust, scalable, and maintainable API integration across the entire EveryFash platform.

---

## 🛠️ Advanced Implementation Patterns

### Pattern 1: FormData Construction for File Uploads

```typescript
// API Function: products.ts
createProduct: async (data: CreateProductOrBaleData) => {
  const formData = new FormData();

  // Basic fields
  formData.append('type', data.type);
  formData.append('name', data.name);
  formData.append('description', data.description);
  formData.append('basePrice', data.basePrice.toString());

  // Nested objects with bracket notation
  if (data.dimensions) {
    formData.append('dimensions[length]', data.dimensions.length?.toString() || '');
    formData.append('dimensions[width]', data.dimensions.width?.toString() || '');
    formData.append('dimensions[height]', data.dimensions.height?.toString() || '');
  }

  // Arrays
  data.highlights?.forEach((highlight, index) => {
    formData.append(`highlights[${index}]`, highlight);
  });

  // Files
  data.productImages?.forEach((image) => {
    formData.append('productImages', image);
  });

  // Variations array with nested objects
  data.variations?.forEach((variation, index) => {
    formData.append(`variations[${index}][color]`, variation.color || '');
    formData.append(`variations[${index}][size]`, variation.size || '');
    formData.append(`variations[${index}][quantity]`, variation.quantity?.toString() || '0');
    formData.append(`variations[${index}][price]`, variation.price?.toString() || '0');
  });

  return await apiClient.postFormData<CreateProductResponse>(
    API_ENDPOINTS.CREATOR_PRODUCTS.CREATE,
    formData
  );
}
```

### Pattern 2: Conditional Authentication

```typescript
// API Function: buyer-products.ts
getProducts: async (params?: BuyerProductsQueryParams) => {
  const endpoint = `${API_ENDPOINTS.PRODUCTS.LIST}${queryString}`;

  // Check if user is authenticated
  const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;

  if (token) {
    // Use authenticated request (gets personalized results)
    return await apiClient.get<BuyerProductsResponse>(endpoint);
  } else {
    // Use public request (no auth header)
    return await apiClient.publicGet<BuyerProductsResponse>(endpoint);
  }
}
```

### Pattern 3: Optimistic Updates

```typescript
// Hook: use-products.ts
export const useUpdateProductVariation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, variationId, data }: {
      productId: string;
      variationId: string;
      data: UpdateVariationData;
    }) => productsApi.updateProductVariation(productId, variationId, data),

    // Optimistic update
    onMutate: async ({ productId, variationId, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: productKeys.detail(productId) });

      // Snapshot previous value
      const previousProduct = queryClient.getQueryData(productKeys.detail(productId));

      // Optimistically update cache
      queryClient.setQueryData(productKeys.detail(productId), (old: any) => {
        if (!old) return old;

        return {
          ...old,
          data: {
            ...old.data,
            product: {
              ...old.data.product,
              variations: old.data.product.variations.map((v: any) =>
                v._id === variationId ? { ...v, ...data } : v
              )
            }
          }
        };
      });

      return { previousProduct };
    },

    // Revert on error
    onError: (err, variables, context) => {
      if (context?.previousProduct) {
        queryClient.setQueryData(productKeys.detail(variables.productId), context.previousProduct);
      }
    },

    // Always refetch after mutation
    onSettled: (data, error, variables) => {
      queryClient.invalidateQueries({ queryKey: productKeys.detail(variables.productId) });
    },
  });
};
```

### Pattern 4: Error Boundary Integration

```typescript
// Component: ProductDetailsPage.tsx
export default function ProductDetailsPage({ params }: { params: { id: string } }) {
  const {
    data: productResponse,
    isLoading,
    error,
    isError
  } = useCreatorProduct(params.id);

  // Handle errors with user feedback
  useEffect(() => {
    if (isError && error) {
      toast({
        variant: "destructive",
        title: "Failed to load product",
        description: "There was an issue loading the product details. Please try again.",
      });
    }
  }, [isError, error]);

  // Loading state
  if (isLoading) {
    return <LottieLoader text="Loading product details..." />;
  }

  // Error state
  if (error || !productResponse?.data?.product) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Product not found</h2>
        <p className="text-gray-600">The product you're looking for doesn't exist or has been removed.</p>
      </div>
    );
  }

  const product = productResponse.data.product;

  return (
    <div>
      {/* Product details rendering */}
    </div>
  );
}
```

---

## 🔧 Debugging and Monitoring

### Request Logging

All API requests are automatically logged through interceptors:

```typescript
// Interceptor: interceptors.ts
{
  name: 'logger',
  priority: 1,
  onResponse: async (response, context) => {
    const duration = Date.now() - context.startTime;
    console.log(`[API] ${context.method} ${context.url} - ${response.status} (${duration}ms)`);
    return response;
  },
  onError: async (error, context) => {
    const duration = Date.now() - context.startTime;
    console.error(`[API] ${context.method} ${context.url} - ERROR (${duration}ms):`, error);
    return error;
  }
}
```

### Performance Monitoring

```typescript
// Interceptor: interceptors.ts
{
  name: 'performance',
  priority: 4,
  onResponse: async (response, context) => {
    const duration = Date.now() - context.startTime;

    // Log slow requests
    if (duration > 5000) {
      console.warn(`[API] Slow request detected: ${context.method} ${context.url} (${duration}ms)`);
    }

    // Store metrics for analytics
    this.recordPerformanceMetric(context, duration, response.status);

    return response;
  }
}
```

### Debug Mode

```typescript
// Enable detailed logging in development
if (process.env.NODE_ENV === 'development') {
  interceptorManager.enableLogging();

  // Log FormData contents
  console.log('=== FormData Debug ===');
  for (let [key, value] of formData.entries()) {
    if (value instanceof File) {
      console.log(`${key}: [File] ${value.name} (${value.size} bytes)`);
    } else {
      console.log(`${key}: ${value}`);
    }
  }
  console.log('=== End FormData Debug ===');
}
```

---

## 📚 Best Practices Summary

### 1. API Function Design
- Always use TypeScript interfaces for request/response types
- Handle FormData construction consistently with bracket notation
- Include comprehensive error handling
- Use request deduplication for frequently called functions

### 2. React Query Integration
- Design hierarchical query keys for efficient cache invalidation
- Implement smart retry logic based on error types
- Use optimistic updates for better UX
- Set appropriate staleTime and gcTime based on data volatility

### 3. Component Integration
- Use hooks for all API interactions
- Handle loading, error, and success states consistently
- Provide user feedback through toast notifications
- Implement proper error boundaries

### 4. Authentication Flow
- All creator endpoints automatically include Bearer tokens
- Public endpoints support optional authentication
- Automatic session management and logout on token expiration
- Role-based access control through AuthManager

### 5. Error Handling
- Centralized error processing through ApiErrorHandler
- User-friendly error messages
- Automatic retry with exponential backoff
- Network error detection and handling

This comprehensive architecture provides a robust foundation for all API interactions in the EveryFash platform, ensuring consistency, reliability, and excellent user experience.

---

## 🚀 Practical Usage Examples

### Example 1: Product Management Component

```typescript
// Component: ProductManagement.tsx
import { useCreatorProducts, useCreateProduct, useUpdateProduct, useDeleteProduct } from '@/lib/hooks/use-products';

export default function ProductManagement() {
  const [filters, setFilters] = useState({ type: 'product', page: 1 });

  // Queries with automatic authorization
  const { data: products, isLoading, error } = useCreatorProducts(filters);
  const { data: counts } = useProductCounts({ type: filters.type });

  // Mutations with automatic authorization
  const createMutation = useCreateProduct();
  const updateMutation = useUpdateProduct();
  const deleteMutation = useDeleteProduct();

  const handleCreate = async (productData: CreateProductData) => {
    try {
      await createMutation.mutateAsync(productData);
      // Success toast shown automatically
      // Cache invalidated automatically
    } catch (error) {
      // Error toast shown automatically
    }
  };

  const handleUpdate = async (productId: string, updates: Partial<CreateProductData>) => {
    try {
      await updateMutation.mutateAsync({ productId, data: updates });
      // Optimistic update applied automatically
      // Success toast shown automatically
    } catch (error) {
      // Error toast shown automatically
      // Optimistic update reverted automatically
    }
  };

  return (
    <div>
      {/* All API calls include Authorization: Bearer <token> automatically */}
      <ProductList
        products={products?.data?.products || []}
        onUpdate={handleUpdate}
        onDelete={(id) => deleteMutation.mutate(id)}
        loading={isLoading}
      />
    </div>
  );
}
```

### Example 2: Real-time Product Editor

```typescript
// Component: ProductEditor.tsx
export default function ProductEditor({ productId }: { productId: string }) {
  // Get product data with automatic authorization
  const { data: productResponse, isLoading } = useCreatorProduct(productId);

  // Individual update mutations
  const updateBasicInfo = useUpdateProductBasicInfo();
  const updateSpecifications = useUpdateProductSpecifications();
  const updateVariations = useUpdateProductVariations();
  const updateImages = useUpdateProductImages();

  const product = productResponse?.data?.product;

  const handleBasicInfoUpdate = async (basicInfo: BasicInfoData) => {
    try {
      await updateBasicInfo.mutateAsync({ productId, data: basicInfo });
      // PATCH /creators/products/{id}/basic-info with Authorization: Bearer <token>
    } catch (error) {
      // Error handled automatically
    }
  };

  const handleSpecificationsUpdate = async (specs: SpecificationsData) => {
    try {
      await updateSpecifications.mutateAsync({ productId, data: specs });
      // PATCH /creators/products/{id}/specifications with Authorization: Bearer <token>
    } catch (error) {
      // Error handled automatically
    }
  };

  const handleVariationsUpdate = async (variations: VariationData[]) => {
    try {
      await updateVariations.mutateAsync({
        productId,
        data: {
          variations: variations.map(v => ({
            _id: v._id, // Include for existing variations
            color: v.color,
            size: v.size,
            quantity: v.quantity,
            price: v.price,
            salePrice: v.salePrice,
            saleEndDate: v.saleEndDate,
          }))
        }
      });
      // PATCH /creators/products/{id}/variations with Authorization: Bearer <token>
    } catch (error) {
      // Error handled automatically
    }
  };

  const handleImagesUpdate = async (imageData: { images?: string[]; productImages?: File[] }) => {
    try {
      await updateImages.mutateAsync({ productId, data: imageData });
      // PATCH /creators/products/{id}/images with Authorization: Bearer <token>
      // FormData automatically constructed with proper file handling
    } catch (error) {
      // Error handled automatically
    }
  };

  if (isLoading) return <LottieLoader text="Loading product..." />;

  return (
    <div>
      <BasicInfoEditor
        product={product}
        onUpdate={handleBasicInfoUpdate}
        loading={updateBasicInfo.isPending}
      />

      <SpecificationsEditor
        product={product}
        onUpdate={handleSpecificationsUpdate}
        loading={updateSpecifications.isPending}
      />

      <VariationsEditor
        product={product}
        onUpdate={handleVariationsUpdate}
        loading={updateVariations.isPending}
      />

      <ImagesEditor
        product={product}
        onUpdate={handleImagesUpdate}
        loading={updateImages.isPending}
      />
    </div>
  );
}
```

### Example 3: Authorization Flow Visualization

```typescript
// Complete request flow for any creator products endpoint:

1. Component calls hook:
   const { data } = useCreatorProducts();

2. Hook calls API function:
   queryFn: () => productsApi.getCreatorProducts()

3. API function calls BaseApiClient:
   return await apiClient.get('/creators/products');

4. BaseApiClient adds authorization:
   headers.Authorization = AuthManager.getAuthHeader(); // "Bearer eyJhbGciOiJIUzI1NiIs..."

5. Request sent with headers:
   {
     'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
     'Content-Type': 'application/json',
     'Accept': 'application/json'
   }

6. Backend validates token and returns data

7. Response processed through interceptors:
   - Logging: "[API] GET /creators/products - 200 (245ms)"
   - Performance: Check for slow requests
   - Error handling: Handle 401/403/500 errors

8. Data returned to component with automatic cache update
```

---

## 🔍 Authorization Header Details

### Token Format
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NzU5YjE4ZDc4ZjQyMzAwMTJhYzQwYjQiLCJyb2xlIjoiY3JlYXRvciIsImlhdCI6MTczNDA5NjI2OSwiZXhwIjoxNzM0MTgyNjY5fQ.signature
```

### Token Payload Structure
```json
{
  "sub": "6759b18d78f4230012ac40b4", // User ID
  "role": "creator",                  // User role
  "iat": 1734096269,                 // Issued at
  "exp": 1734182669                  // Expires at
}
```

### Automatic Token Management
- **Storage**: localStorage with metadata
- **Validation**: Automatic expiration checking
- **Refresh**: Placeholder for future refresh token implementation
- **Cleanup**: Automatic removal on logout/expiration
- **Role Checking**: Built-in role validation

---

## 📋 Quick Reference

### All Creator Products Endpoints with Authorization

| Method | Endpoint | Function | Authorization |
|--------|----------|----------|---------------|
| GET | `/creators/products` | `getCreatorProducts()` | ✅ Bearer Token |
| POST | `/creators/products` | `createProduct()` | ✅ Bearer Token |
| GET | `/creators/products/{id}` | `getCreatorProduct()` | ✅ Bearer Token |
| PATCH | `/creators/products/{id}` | `updateProduct()` | ✅ Bearer Token |
| DELETE | `/creators/products/{id}` | `deleteProduct()` | ✅ Bearer Token |
| GET | `/creators/products/counts` | `getProductCounts()` | ✅ Bearer Token |
| PATCH | `/creators/products/{id}/specifications` | `updateProductSpecifications()` | ✅ Bearer Token |
| PATCH | `/creators/products/{id}/basic-info` | `updateProductBasicInfo()` | ✅ Bearer Token |
| PATCH | `/creators/products/{id}/variations` | `updateProductVariations()` | ✅ Bearer Token |
| PATCH | `/creators/products/{id}/variations/{variationId}` | `updateProductVariation()` | ✅ Bearer Token |
| DELETE | `/creators/products/{id}/variations/{variationId}` | `deleteProductVariation()` | ✅ Bearer Token |
| PATCH | `/creators/products/{id}/images` | `updateProductImages()` | ✅ Bearer Token |

### Public Products Endpoints (Optional Authorization)

| Method | Endpoint | Function | Authorization |
|--------|----------|----------|---------------|
| GET | `/products` | `buyerProductsApi.getProducts()` | 🔄 Optional |
| GET | `/products/{id}` | `buyerProductsApi.getProduct()` | 🔄 Optional |
| GET | `/buyers/products/{id}/related` | `buyerProductsApi.getRelatedProducts()` | ✅ Required |
| GET | `/buyers/products/{id}/shop-products` | `buyerProductsApi.getShopProducts()` | ✅ Required |

**Legend:**
- ✅ Bearer Token: Always includes `Authorization: Bearer <token>`
- 🔄 Optional: Includes token if user is logged in, otherwise public request
- ❌ Public: Never includes authorization header

This architecture ensures that all creator-specific operations are properly authenticated while maintaining flexibility for public endpoints.
```
