import { useQuery } from '@tanstack/react-query';
import { searchApi, handleSearchApiError } from '@/lib/api';

export interface SearchFilters {
  q?: string;
  category?: string;
  brands?: string;
  sizes?: string;
  colors?: string;
  gender?: string;
  type?: string;
  discountRange?: string;
  countries?: string;
  conditions?: string;
  minPrice?: string;
  maxPrice?: string;
  sort?: string;
  page?: number;
  limit?: number;
}

export interface Product {
  _id: string;
  type: 'product';
  name: string;
  images: string[];
  basePrice: number;
  status: string;
  maxDiscountPercentage: number;
  ratingsAverage: number;
  ratingsQuantity: number;
  formattedPriceRange: {
    original: {
      min: number;
      max: number;
      isSinglePrice: boolean;
    };
    discounted: {
      min: number;
      max: number;
      isSinglePrice: boolean;
    } | null;
    discountPercentage: number;
  };
  hasAnyDiscount: boolean;
  availableColors: string[];
  availableSizes: string[];
  totalItemsLeft: number;
  brand: string;
  gender: string;
  shop: {
    name: string;
    logo: string;
  };
}

export interface Bale {
  _id: string;
  type: 'bale';
  name: string;
  images: string[];
  basePrice: number;
  status: string;
  maxDiscountPercentage: number;
  ratingsAverage: number;
  ratingsQuantity: number;
  formattedPriceRange: {
    original: {
      min: number;
      max: number;
      isSinglePrice: boolean;
    };
    discounted: {
      min: number;
      max: number;
      isSinglePrice: boolean;
    } | null;
    discountPercentage: number;
  };
  hasAnyDiscount: boolean;
  availableColors: string[];
  availableSizes: string[];
  availableIdentifiers?: string[]; // Optional for bales - may not be returned by all APIs
  totalItemsLeft: number;
  brand: string;
  gender: string;
  shop: {
    name: string;
    logo: string;
  };
}

export interface Shop {
  _id: string;
  type: 'shop';
  name: string;
  logo: string;
  description: string;
  followers: number;
  metrics: {
    totalProducts: number;
    totalBales: number;
    averageRating: number;
    totalSales: number;
    qualityScore: number;
  };
}

export interface SearchResponse {
  status: string;
  results: number;
  total: number;
  page: number;
  limit: number;
  data: {
    products: Product[];
    bales: Bale[];
    shops: Shop[];
  };
}

// Query keys
export const searchKeys = {
  all: ['search'] as const,
  lists: () => [...searchKeys.all, 'list'] as const,
  list: (filters: SearchFilters) => [...searchKeys.lists(), filters] as const,
};

// Search query hook
export const useSearch = (filters: SearchFilters, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: searchKeys.list(filters),
    queryFn: async (): Promise<SearchResponse> => {
      try {
        const response = await searchApi.search(filters);
        
        if (response.status === 'success') {
          return response;
        } else {
          throw new Error('Invalid response format');
        }
      } catch (error) {
        const errorMessage = handleSearchApiError(error);
        throw new Error(errorMessage);
      }
    },
    enabled: options?.enabled !== false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors except 408, 429
      if (error instanceof Error) {
        const status = (error as any).status;
        if (status >= 400 && status < 500 && status !== 408 && status !== 429) {
          return false;
        }
      }
      return failureCount < 3;
    },
  });
};
