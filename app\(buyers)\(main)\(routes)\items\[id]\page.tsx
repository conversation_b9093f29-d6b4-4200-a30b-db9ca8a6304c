'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import ProductDetailsCarousel from '@/app/(buyers)/(main)/_components/ProductDetailsCarousel';
import ProductDetailsComponent from '@/app/(buyers)/(main)/_components/ProductDetailsComponent';
import Image from 'next/image';
import { useBuyerProduct } from '@/lib/hooks/use-buyer-products';
import LottieLoader from '@/components/ui/LottieLoader';
import { toast } from '@/hooks/use-toast';

const Product = () => {
  const params = useParams();
  const productId = params.id as string;

  // Fetch product details using the API
  const { data: productResponse, isLoading, error } = useBuyerProduct(productId);

  // Show loading state
  if (isLoading) {
    return (
      <section className="w-full mt-20 flex items-center justify-center py-12">
        <LottieLoader
          size="lg"
          text="Loading product details..."
          textSize="md"
          centered={true}
        />
      </section>
    );
  }

  // Show error state
  if (error || !productResponse?.data?.product) {
    // Show error toast
    React.useEffect(() => {
      if (error) {
        toast({
          variant: "destructive",
          title: "Failed to load product",
          description: "There was an issue loading the product details. Please try again.",
        });
      }
    }, [error]);

    return (
      <section className="w-full mt-20 flex items-center justify-center py-12">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Product not found</h2>
          <p className="text-gray-600">The product you're looking for doesn't exist or has been removed.</p>
        </div>
      </section>
    );
  }

  const product = productResponse.data.product;
  console.log("Product", product)
  // Transform API data to match component expectations


    const colorsSet = new Set<string>();
    const sizesSet = new Set<string>();
    const identifiersSet = new Set<string>();
    const unavailableVariants: { label: string; reason: string }[] = [];
    let totalStock = 0;

    const variations = (product.variations || []).map(v => {
      const { _id, color, size, identifier, quantity = 0, currentPrice, price, isLowStock, isOutOfStock } = v;

      // Collect unique attributes
      if (color) colorsSet.add(color);
      if (size) sizesSet.add(size);
      if (identifier) identifiersSet.add(identifier);

      // Stock calculations
      totalStock += quantity;

      // Mark unavailable variants
      if (quantity <= 0) {
        unavailableVariants.push({
          label: size || color || identifier || 'Unknown',
          reason: isOutOfStock ? 'Out of Stock' : 'Unavailable'
        });
      }

      return {
        id: _id,
        color: color || null,
        size: size || null,
        identifier: identifier || null,
        quantity,
        price: currentPrice ?? price ?? 0,
        isLowStock: !!isLowStock,
        isOutOfStock: !!isOutOfStock
      };
    });

    const transformedProduct = {
      id: product._id,
      type: product.type,
      slug: product.slug,
      name: product.name || '',
      brand: product.brand || '',
      description: product.description || '',
      highlights: product.highlights || [],
      gender: product.gender || '',
      basePrice: product.basePrice || 0,

      category: {
        id: product.category?._id || '',
        name: product.category?.name || '',
        description: product.category?.description || '',
        pathArray: product.category?.pathArray || []
      },

      relatedCategories: product.relatedCategories?.map(c => ({
        id: c._id,
        name: c.name,
        description: c.description,
        pathArray: c.pathArray
      })) || [],

      tags: product.tags || [],

      creator: {
        id: product.creator?._id || '',
        userType: product.creator?.userType || '',
        metrics: product.creator?.metrics || {},
        shopInfo: {
          name: product.creator?.shopInfo?.name || '',
          description: product.creator?.shopInfo?.description || '',
          logo: product.creator?.shopInfo?.logo || '',
          banner: product.creator?.shopInfo?.banner || ''
        }
      },

      images: product.images || [],
      specifications: product.specifications || {},
      condition: product.condition || '',
      variations,

      availableColors: Array.from(colorsSet),
      availableSizes: Array.from(sizesSet),
      availableIdentifiers: Array.from(identifiersSet),

      unavailableVariants,
      sold: product.sold || 0,
      stock: totalStock,

      priceRange: {
        min: product.priceRange?.discounted?.min ?? product.priceRange?.original?.min ?? 0,
        max: product.priceRange?.discounted?.max ?? product.priceRange?.original?.max ?? 0,
        discountPercentage: product.priceRange?.discountPercentage || 0
      },

      promotions: product.promotions || [],
      activePromotion: product.activePromotion || null,

      reviewStats: {
        averageRating: product.reviewStats?.averageRating || 0,
        totalReviews: product.reviewStats?.totalReviews || 0,
        reviews: product.reviewStats?.reviews || []
      },

      status: product.status || '',
      featured: product.featured || false,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt
    };



  return (
    <section className="w-full mt-20">
      {/* Product Images Carousel */}
      <ProductDetailsCarousel>
        {transformedProduct.images.map((image, index) => (
          <section
            key={index}
            className="h-[50vh] w-full flex justify-center items-center bg-white rounded-xl shadow-md overflow-hidden"
          >
            <Image
              src={image}
              alt={`${transformedProduct.name} - Image ${index + 1}`}
              width={500}
              height={500}
              className="object-contain h-full w-auto"
            />
          </section>
        ))}
      </ProductDetailsCarousel>

      {/* Product Details Component */}
      <ProductDetailsComponent product={transformedProduct} />

    </section>
  );
};

export default Product;