import { useQuery } from '@tanstack/react-query';
import { BaseApiClient } from '@/lib/api/base-client';
import { API_ENDPOINTS } from '@/lib/api/config';
import { handleApiError } from '@/lib/api/errors';

// Create API client instance
const apiClient = new BaseApiClient();

// Types for the filter parameters API response
export interface CategoryNode {
  _id: string;
  name: string;
  description: string;
  pathArray: string[];
  children: CategoryNode[];
}

export interface SortOption {
  value: string;
  label: string;
}

export interface FilterParamsData {
  categories: CategoryNode[];
  brands: string[];
  countries: string[];
  conditions: string[];
  sizes: string[];
  identifiers: string[];
  genders: string[];
  priceRange: {
    min: number;
    max: number;
  };
  sortOptions: SortOption[];
}

export interface FilterParamsResponse {
  status: string;
  data: FilterParamsData;
}

// Query keys for filter params
export const filterParamsKeys = {
  all: ['filter-params'] as const,
  params: () => [...filterParamsKeys.all, 'params'] as const,
};



// Helper function to flatten categories for easier searching/filtering
export const flattenCategories = (categories: CategoryNode[]): CategoryNode[] => {
  const flattened: CategoryNode[] = [];
  
  const flatten = (nodes: CategoryNode[]) => {
    nodes.forEach(node => {
      flattened.push(node);
      if (node.children && node.children.length > 0) {
        flatten(node.children);
      }
    });
  };
  
  flatten(categories);
  return flattened;
};

// Helper function to get category display name (last part of path)
export const getCategoryDisplayName = (category: CategoryNode): string => {
  return category.description || category.pathArray[category.pathArray.length - 1] || category.name;
};

// Helper function to get category full path as string
export const getCategoryFullPath = (category: CategoryNode): string => {
  return category.pathArray.join(' > ');
};
