"use client";

import React from 'react';
import ProductsCarousel from './ProductsCarousel';

interface ProductCarouselSkeletonProps {
  title: string;
  itemCount?: number;
}

const ProductCarouselSkeleton: React.FC<ProductCarouselSkeletonProps> = ({ 
  title, 
  itemCount = 4 
}) => {
  return (
    <section className='bg-white my-2 py-3 px-3 rounded shadow-sm'>
      <h1 className="text-sm font-semibold text-gray-600 pb-2">{title}</h1>
      <ProductsCarousel>
        {[...Array(itemCount)].map((_, index) => (
          <div key={index} className="p-1">
            <div className="animate-pulse">
              {/* Product Image Skeleton */}
              <div className="bg-gray-200 aspect-square rounded-lg mb-3"></div>
              
              {/* Product Info Skeleton */}
              <div className="space-y-2">
                {/* Product Name */}
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                
                {/* Price */}
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                
                {/* Colors/Variations */}
                <div className="flex space-x-1">
                  <div className="h-3 w-3 bg-gray-200 rounded-full"></div>
                  <div className="h-3 w-3 bg-gray-200 rounded-full"></div>
                  <div className="h-3 w-3 bg-gray-200 rounded-full"></div>
                </div>
                
                {/* Rating */}
                <div className="flex items-center space-x-1">
                  <div className="h-3 bg-gray-200 rounded w-12"></div>
                  <div className="h-3 bg-gray-200 rounded w-8"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </ProductsCarousel>
    </section>
  );
};

export default ProductCarouselSkeleton;
